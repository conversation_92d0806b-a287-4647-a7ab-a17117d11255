import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { User, AuthState } from '@/types/user';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile as firebaseUpdateProfile,
  GoogleAuthProvider,
  signInWithPopup,
  sendPasswordResetEmail,
  updateEmail,
  updatePassword as firebaseUpdatePassword,

  sendEmailVerification as firebaseSendEmailVerification,
  applyActionCode,
  signInWithRedirect,
  getRedirectResult,
  browserLocalPersistence,
  browserSessionPersistence,
  setPersistence
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, firestore } from '@/config/firebase';
import { Platform } from 'react-native';
import { clearAllUserStores } from '@/utils/clear-stores';

// Helper function to parse Firebase error messages
const parseFirebaseError = (error: any): string => {
  const errorCode = error.code || '';

  // Map Firebase error codes to user-friendly messages
  const errorMessages: Record<string, string> = {
    'auth/email-already-in-use': 'This email is already registered. Please use a different email or try logging in.',
    'auth/invalid-email': 'Please enter a valid email address.',
    'auth/user-disabled': 'This account has been disabled. Please contact support.',
    'auth/user-not-found': 'No account found with this email. Please check your email or sign up.',
    'auth/wrong-password': 'Incorrect password. Please try again or reset your password.',
    'auth/weak-password': 'Password is too weak. Please use at least 6 characters.',
    'auth/invalid-credential': 'Invalid login credentials. Please check your email and password.',
    'auth/too-many-requests': 'Too many unsuccessful login attempts. Please try again later.',
    'auth/network-request-failed': 'Network error. Please check your internet connection and try again.',
    'auth/popup-closed-by-user': 'Google sign-in was cancelled. Please try again.',
    'auth/operation-not-allowed': 'This operation is not allowed. Please contact support.',
    'auth/requires-recent-login': 'This action requires recent authentication. Please log in again.',
    'auth/email-already-exists': 'The email address is already in use by another account.',
    'auth/invalid-action-code': 'The verification link has expired or is invalid. Please request a new one.',
    'auth/account-exists-with-different-credential': 'An account already exists with the same email address but different sign-in credentials.',
    'auth/expired-action-code': 'The verification link has expired. Please request a new one.',
    'auth/invalid-verification-code': 'The verification code is invalid. Please try again.',
    'auth/missing-verification-code': 'Please enter the verification code.',
    'auth/quota-exceeded': 'Quota exceeded. Please try again later.',
    'auth/provider-already-linked': 'This account is already linked with another provider.',
    'auth/credential-already-in-use': 'This credential is already linked to another account.',
    'auth/missing-verification-id': 'Verification ID is missing. Please request a new code.',
    'auth/app-not-authorized': 'This app is not authorized to use Firebase Authentication.',
    'auth/argument-error': 'Invalid argument provided to Firebase Authentication method.',
    'auth/invalid-continue-uri': 'The continue URL provided is invalid.',
    'auth/unauthorized-continue-uri': 'The domain of the continue URL is not whitelisted.',
    'auth/unauthorized-domain': 'This domain is not authorized for OAuth operations.',
    'auth/invalid-verification-id': 'The verification ID is invalid. Please request a new code.',
    'auth/permission-denied': 'Authentication failed due to permission issues. Please contact support.',
  };

  // Return user-friendly message or default error message
  return errorMessages[errorCode] || `Authentication error: ${error.message || 'Unknown error occurred'}`;
};

// Initialize the auth store with Zustand
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Initialize auth state on store creation
      init: () => {
        console.log('Auth store initialized');
        // Check if there's a current user immediately
        const currentUser = auth.currentUser;
        if (currentUser) {
          console.log('Found existing user on init:', currentUser.uid);
          // Trigger auth state check
          get().checkAuthState();
        } else {
          console.log('No existing user on init');
          set({ isLoading: false });
        }
      },

      // Check if user is authenticated
      checkAuthState: async () => {
        console.log('Starting checkAuthState...');
        set({ isLoading: true, error: null });

        try {
          // Get current user immediately first
          const currentUser = auth.currentUser;
          console.log('Current user:', currentUser?.uid || 'none');

          if (currentUser) {
            // User is already signed in, get their data
            try {
              const userDoc = await getDoc(doc(firestore, 'users', currentUser.uid));

              if (userDoc.exists()) {
                const userData = userDoc.data() as User;

                // Check if email is verified
                if (!currentUser.emailVerified) {
                  console.log('User email not verified');
                  set({
                    isAuthenticated: false,
                    user: {
                      ...userData,
                      id: currentUser.uid,
                      emailVerified: false
                    },
                    isLoading: false,
                    error: 'Please verify your email to continue.'
                  });
                  return;
                }

                // User is fully authenticated
                console.log('User is authenticated:', userData.email);
                set({
                  user: {
                    ...userData,
                    id: currentUser.uid,
                    emailVerified: currentUser.emailVerified
                  },
                  isAuthenticated: true,
                  isLoading: false,
                  error: null
                });
                return;
              } else {
                console.log('User document not found');
                set({
                  isAuthenticated: false,
                  user: null,
                  isLoading: false,
                  error: 'User data not found'
                });
                return;
              }
            } catch (error) {
              console.error('Error fetching user data:', error);
              set({
                isAuthenticated: false,
                user: null,
                isLoading: false,
                error: 'Error fetching user data'
              });
              return;
            }
          } else {
            // No current user
            console.log('No current user found');
            set({
              isAuthenticated: false,
              user: null,
              isLoading: false,
              error: null
            });
            return;
          }
        } catch (error) {
          console.error('Error checking auth state:', error);
          set({
            isAuthenticated: false,
            user: null,
            isLoading: false,
            error: 'Error checking authentication state'
          });
        }
      },

      // Register a new user
      register: async (email: string, password: string, name: string) => {
        set({ isLoading: true, error: null });

        try {
          // Create user in Firebase Auth
          const userCredential = await createUserWithEmailAndPassword(auth, email, password);
          const user = userCredential.user;

          // Update profile with display name
          await firebaseUpdateProfile(user, {
            displayName: name,
          });

          // Send email verification
          await firebaseSendEmailVerification(user);

          // Create user document in Firestore with owner role for multi-tenancy
          const timestamp = Date.now();
          await setDoc(doc(firestore, 'users', user.uid), {
            id: user.uid,
            email: email,
            name: name,
            role: 'owner', // New users are owners (tenants) by default
            language: 'en',
            preferAudio: false,
            offlineMode: false,
            emailVerified: false,
            createdAt: timestamp,
            updatedAt: timestamp,
            ownedFarms: [], // Initialize empty array of owned farms
          });

          set({
            isLoading: false,
            error: null,
            user: {
              id: user.uid,
              email: email,
              name: name,
              role: 'owner', // New users are owners (tenants) by default
              language: 'en',
              preferAudio: false,
              offlineMode: false,
              emailVerified: false,
              createdAt: timestamp,
              updatedAt: timestamp,
              ownedFarms: [], // Initialize empty array of owned farms
            },
            isAuthenticated: false
          });

          return user.uid;
        } catch (error: any) {
          console.error('Registration error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }
      },

      // Login with email and password
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          // Set persistence based on platform
          if (Platform.OS === 'web') {
            await setPersistence(auth, browserLocalPersistence);
          }

          // Sign in with Firebase Auth
          const userCredential = await signInWithEmailAndPassword(auth, email, password);
          const user = userCredential.user;

          // Check if email is verified
          if (!user.emailVerified) {
            set({
              isLoading: false,
              error: 'Please verify your email to continue. Check your inbox for a verification link.',
              isAuthenticated: false,
              user: {
                id: user.uid,
                email: user.email || '',
                name: user.displayName || '',
                role: 'owner', // Default role for unverified users
                language: 'en',
                preferAudio: false,
                offlineMode: false,
                emailVerified: false,
                createdAt: Date.now(),
                updatedAt: Date.now(),
              }
            });
            return;
          }

          // Get additional user data from Firestore
          const userDoc = await getDoc(doc(firestore, 'users', user.uid));

          if (userDoc.exists()) {
            const userData = userDoc.data() as User;

            // Update last login timestamp
            await updateDoc(doc(firestore, 'users', user.uid), {
              updatedAt: Date.now(),
              emailVerified: user.emailVerified
            });

            // Set authenticated user state
            set({
              user: {
                ...userData,
                id: user.uid,
                emailVerified: user.emailVerified
              },
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            // Create user document if it doesn't exist (fallback)
            // This is a rare case but might happen if auth exists but Firestore document was deleted
            const timestamp = Date.now();
            await setDoc(doc(firestore, 'users', user.uid), {
              id: user.uid,
              email: user.email || '',
              name: user.displayName || '',
              role: 'owner', // Default to owner role for multi-tenancy
              language: 'en',
              preferAudio: false,
              offlineMode: false,
              emailVerified: user.emailVerified,
              createdAt: timestamp,
              updatedAt: timestamp,
              ownedFarms: [], // Initialize empty array of owned farms
            });

            // Set authenticated user state
            set({
              user: {
                id: user.uid,
                email: user.email || '',
                name: user.displayName || '',
                role: 'owner', // Default to owner role for multi-tenancy
                language: 'en',
                preferAudio: false,
                offlineMode: false,
                emailVerified: user.emailVerified,
                createdAt: timestamp,
                updatedAt: timestamp,
                ownedFarms: [], // Initialize empty array of owned farms
              },
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          }
        } catch (error: any) {
          console.error('Login error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }
      },

      // Login with Google
      loginWithGoogle: async () => {
        set({ isLoading: true, error: null });

        try {
          if (Platform.OS === 'web') {
            // Web implementation
            const provider = new GoogleAuthProvider();

            // Try to get redirect result first (in case we're returning from a redirect)
            try {
              const result = await getRedirectResult(auth);
              if (result && result.user) {
                // User successfully signed in with redirect
                const user = result.user;
                await handleGoogleSignInSuccess(user);
                return;
              }
            } catch (redirectError) {
              console.error('Redirect result error:', redirectError);
              // Continue with popup or redirect
            }

            // Try popup first, fall back to redirect
            try {
              const result = await signInWithPopup(auth, provider);
              const user = result.user;
              await handleGoogleSignInSuccess(user);
            } catch (popupError: any) {
              console.error('Popup sign-in error:', popupError);

              // If popup fails (e.g., popup blocked), try redirect
              if (popupError.code === 'auth/popup-blocked' ||
                  popupError.code === 'auth/popup-closed-by-user') {
                await signInWithRedirect(auth, provider);
              } else {
                throw popupError;
              }
            }
          } else {
            // Mobile implementation would go here
            // This would typically use Expo AuthSession or react-native-google-signin
            set({ isLoading: false, error: 'Google sign-in is not implemented for mobile yet' });
            throw new Error('Google sign-in is not implemented for mobile yet');
          }
        } catch (error: any) {
          console.error('Google login error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }

        // Helper function to handle successful Google sign-in
        async function handleGoogleSignInSuccess(user: any) {
          // Check if user document exists
          const userDoc = await getDoc(doc(firestore, 'users', user.uid));

          if (!userDoc.exists()) {
            // Create user document if it doesn't exist
            const timestamp = Date.now();
            await setDoc(doc(firestore, 'users', user.uid), {
              id: user.uid,
              email: user.email || '',
              name: user.displayName || '',
              role: 'owner', // Default to owner role for multi-tenancy
              language: 'en',
              preferAudio: false,
              offlineMode: false,
              emailVerified: user.emailVerified,
              createdAt: timestamp,
              updatedAt: timestamp,
              ownedFarms: [], // Initialize empty array of owned farms
            });
          } else {
            // Update last login timestamp
            await updateDoc(doc(firestore, 'users', user.uid), {
              updatedAt: Date.now(),
              emailVerified: user.emailVerified
            });
          }

          // Get user data
          const updatedUserDoc = await getDoc(doc(firestore, 'users', user.uid));
          const userData = updatedUserDoc.data() as User;

          // Set authenticated user state
          set({
            user: {
              ...userData,
              id: user.uid,
              emailVerified: user.emailVerified
            },
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        }
      },

      // Logout
      logout: async () => {
        set({ isLoading: true, error: null });

        try {
          // Clear all user-specific data from stores first
          await clearAllUserStores();

          // Sign out from Firebase
          await signOut(auth);

          // Set auth state to logged out
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });

          console.log('Logout successful - user state cleared');
        } catch (error: any) {
          console.error('Logout error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }
      },

      // Reset password
      resetPassword: async (email: string) => {
        set({ isLoading: true, error: null });

        try {
          await sendPasswordResetEmail(auth, email);
          set({ isLoading: false, error: null });
        } catch (error: any) {
          console.error('Reset password error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }
      },

      // Update user profile
      updateProfile: async (data: Partial<User>) => {
        set({ isLoading: true, error: null });

        try {
          const { user } = get();

          if (!user) {
            throw new Error('User not authenticated');
          }

          // Update Firebase Auth profile if name is provided
          if (data.name) {
            await firebaseUpdateProfile(auth.currentUser!, {
              displayName: data.name,
            });
          }

          // Update email if provided
          if (data.email && data.email !== user.email) {
            await updateEmail(auth.currentUser!, data.email);
          }

          // Update user document in Firestore
          const userRef = doc(firestore, 'users', user.id);
          await updateDoc(userRef, {
            ...data,
            updatedAt: Date.now(),
          });

          // Get updated user data
          const userDoc = await getDoc(userRef);
          const userData = userDoc.data() as User;

          // Update user state
          set({
            user: {
              ...user,
              ...userData,
            },
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          console.error('Update profile error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }
      },

      // Update password
      updatePassword: async (currentPassword: string, newPassword: string) => {
        set({ isLoading: true, error: null });

        try {
          const { user } = get();

          if (!user) {
            throw new Error('User not authenticated');
          }

          // Re-authenticate user before updating password
          const credential = await signInWithEmailAndPassword(auth, user.email, currentPassword);

          // Update password
          await firebaseUpdatePassword(credential.user, newPassword);

          set({ isLoading: false, error: null });
        } catch (error: any) {
          console.error('Update password error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }
      },

      // Send email verification
      sendEmailVerification: async () => {
        set({ isLoading: true, error: null });

        try {
          if (!auth.currentUser) {
            throw new Error('No user is currently signed in');
          }

          await firebaseSendEmailVerification(auth.currentUser);
          set({ isLoading: false, error: null });
        } catch (error: any) {
          console.error('Send email verification error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }
      },

      // Verify email with action code
      verifyEmail: async (oobCode?: string) => {
        set({ isLoading: true, error: null });

        try {
          if (!oobCode) {
            throw new Error('Verification code is required');
          }
          await applyActionCode(auth, oobCode);

          // If there's a current user, update their emailVerified status
          if (auth.currentUser) {
            // Reload user to get updated emailVerified status
            await auth.currentUser.reload();

            // Update Firestore
            const userRef = doc(firestore, 'users', auth.currentUser.uid);
            await updateDoc(userRef, {
              emailVerified: true,
              updatedAt: Date.now(),
            });

            // Update local state
            const { user } = get();
            if (user) {
              set({
                user: {
                  ...user,
                  emailVerified: true,
                },
                isLoading: false,
                error: null,
              });
            } else {
              set({ isLoading: false, error: null });
            }
          } else {
            set({ isLoading: false, error: null });
          }

          return true;
        } catch (error: any) {
          console.error('Verify email error:', error);
          set({ isLoading: false, error: parseFirebaseError(error) });
          throw error;
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);