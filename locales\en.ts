export const en = {
  common: {
    "caretaker": "Caretaker",
    "noFarmsTitle": "No Farms Available",
    "noFarmsMessage": "Please add a farm to continue using this feature.",
    loading: "Loading...",
    cancel: "Cancel",
    save: "Save",
    delete: "Delete",
    edit: "Edit",
    yes: "Yes",
    no: "No",
    ok: "OK",
    close: "Close",
    confirm: "Confirm",
    back: "Back",
    next: "Next",
    done: "Done",
    search: "Search",
    filter: "Filter",
    all: "All",
    allFarms: "All Farms",
    user: "User",
    error: "Error",
    success: "Success",
    warning: "Warning",
    info: "Info",
    active: "Active",
    inactive: "Inactive",
    permissionDenied: "Permission Denied",
    dismiss: "Dismiss",
    pageNotFound: "This screen doesn't exist.",
    goToHome: "Go to home screen!",
    somethingWentWrong: "Something went wrong",
    checkLogs: "Please check your device logs for more details.",
    selectDate: "Select Date",
    validationError: "Validation Error",
    fillRequiredFields: "Please fill all required fields correctly.",
    notLoggedIn: "You must be logged in to perform this action",
    permissionRequired: "Permission Required",
    cameraPermission: "Camera permission is required to take photos",
    galleryPermission: "Gallery permission is required to select images",
    networkError: "Network error. Please check your connection and try again",
    serverError: "Server error. Please try again later",
    unknownError: "An unknown error occurred. Please try again",
    timeoutError: "Request timed out. Please try again",
    authError: "Authentication error. Please log in again",
    dataLoadError: "Failed to load data. Please try again",
    dataSaveError: "Failed to save data. Please try again",
    dataDeleteError: "Failed to delete data. Please try again",
    invalidInput: "Invalid input. Please check your entries",
    optional: "Optional",
    required: "Required",
    retry: "Retry",
    recordNotFound: "Record not found",
    tryAgain: "Try Again",
    continue: "Continue",
    viewAll: "View All",
    filters: "Filters",
    activeFilters: "Active Filters",
    clear: "Clear",
    date: "Date",
    month: "Month",
    year: "Year",
    selectMonth: "Select Month",
    selectYear: "Select Year",
    unknown: "Unknown",
    notAvailable: "Not Available",
    animal: "animal",
    kg: "kg",
    goBack: "Go Back",
    description: "Description",
    created: "Created",
    updated: "Updated",
    weekdays: {
      sun: "Sun",
      mon: "Mon",
      tue: "Tue",
      wed: "Wed",
      thu: "Thu",
      fri: "Fri",
      sat: "Sat"
    },
    add: "Add",
    createdAt: "Created At",
    lastUpdated: "Last Upadted",
    deactivate: "Deactivate",
    activate: "Activate",
    changePhoto: "Change Photo",
    addPhoto: "Add Photo",
    gender: "Gender",
    role: "Role",
    worker: "Worker",
    admin: "Admin",
    status: "Status",
    male: "Male",
    female: "Female",
    "errorOccurred": "An error occurred",
    "microphonePermissionDenied": "Microphone permission is required to record audio",
    "recordingSaved": "Recording saved successfully",
    "speechRecognitionError": "Error converting speech to text. Please try again.",
    "unsupported": "Feature Not Supported",
    "voiceRecordingWebUnsupported": "Voice recording is not supported in web browsers. Please use the mobile app for this feature.",
    years: "Years",
    searchCategories: "Search Categories",
    apply: "Apply",
    recording: "Recording...",
    recordingStarted: "Voice recording started",
    recordingStopped: "Voice recording stopped",
    recordingFailed: "Failed to start recording",
    tryDifferentSearch: "Try Different Search",

  },

  auth: {
    login: "Login",
    register: "Register",
    forgotPassword: "Forgot Password",
    email: "Email",
    password: "Password",
    confirmPassword: "Confirm Password",
    name: "Name",
    phone: "Phone",
    loginWithGoogle: "Login with Google",
    loginWithOTP: "Login with OTP",
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: "Already have an account?",
    signUp: "Sign Up",
    signIn: "Sign In",
    enterOTP: "Enter OTP",
    verifyOTP: "Verify OTP",
    resendOTP: "Resend OTP",
    otpSent: "Verification email has been sent to your inbox",
    invalidOTP: "Invalid OTP",
    invalidCredentials: "Invalid email or password",
    passwordsDontMatch: "Passwords don't match",
    accountCreated: "Account created successfully",
    verified: "Verified",
    verifyEmailPrompt: "Please verify your email address to access all features",
    verifyNow: "Verify Now",
    currentPassword: "Current Password",
    newPassword: "New Password",
    updateEmail: "Update Email",
    updatePassword: "Update Password",
    emailUpdated: "Email updated successfully",
    passwordUpdated: "Password updated successfully",
    updateFailed: "Update failed. Please try again.",
  },

  dashboard: {
    welcome: "Welcome,",
    sync: "Sync",
    monitoring: "Monitoring animals",
    animals: "animals",
    yourAnimals: "Your Animals",
    sickAnimals: "Sick Animals",
    healthyAnimals: "Healthy Animals",
    totalAnimals: "Total Animals",
    healthChecks: "Health Checks",
    stats: {
      totalAnimals: "Total Animals",
      healthyAnimals: "Healthy Animals",
      healthAlerts: "Health Alerts",
      checksLast30Days: "Checks (30d)",
    },
    overdueChecks: "Overdue Health Checks",
    isDueForCheck: "is due for a health check",
    scheduleCheck: "Perform Health Check",
    healthCheckTypes: "Health Check Types",
    recentHealthChecks: "Recent Health Checks",
    recentHealthRecords: "Recent Health Records",
    viewAll: "View All",
    CaptureorSelectImageforAnalysis: "Capture or Select Image for Analysis",
    Takeaphotooftheanimalscondition: "Take a photo of the animal's condition (e.g., skin, eyes, feces) for AI analysis",
  },

  animals: {
    title: "Animals",
    addAnimal: "Add Animal",
    editAnimal: "Edit Animal",
    animalDetails: "Animal Details",
    name: "Name",
    namePlaceholder: "Enter animal name",
    nameRequired: "Name is required",
    species: "Species",
    speciesPlaceholder: "e.g. Cow, Dog, Goat",
    speciesRequired: "Species is required",
    animalSpecies: "Species",
    breed: "Breed",
    breedPlaceholder: "e.g. Holstein, German Shepherd",
    breedSelectPlaceholder: "Select breed",
    age: "Age (years)",
    agePlaceholder: "e.g. 3",
    ageNumber: "Age must be a number",
    agePositive: "Age must be greater than 0",
    weight: "Weight (kg)",
    weightPlaceholder: "e.g. 450",
    weightInputPlaceholder: "Enter weight in kg",
    weightNumber: "Weight must be a number",
    gender: "Gender",
    male: "Male",
    female: "Female",
    unknown: "Unknown",
    birthdate: "Birth Date (optional)",
    animalBirthdate: "Birth Date",
    color: "Color",
    markings: "Markings",
    tagId: "Tag ID",
    tagIdPlaceholder: "Optional identification tag",
    tagIdInputPlaceholder: "Enter tag ID",
    microchip: "Microchip",
    notes: "Notes",
    lastHealthCheck: "Last Health Check",
    nextHealthCheck: "Next Health Check",
    healthRecords: "Health Records",
    healthChecks: "Health Checks",
    deleteConfirm: "Are you sure you want to delete this animal?",
    deleteSuccess: "Animal deleted successfully",
    noAnimals: "No animals found",
    addAnimalsFirst: "Please add animals to this farm first before recording milking data",
    addYourFirstAnimal: "Add your first animal to get started",
    addPhoto: "Add Photo",
    takePhoto: "Take Photo",
    choosePhoto: "Choose from Library",
    saveAnimal: "Save Animal",
    addError: "Failed to add animal. Please try again.",
    cameraPermission: "We need camera permission to take photos",
    cameraRollPermission: "We need camera roll permission to upload images",
    healthy: "Healthy",
    unhealthy: "Unhealthy",
    needAttention: "Need Attention",
    lastVaccine: "Last vaccine",
    noVaccineYet: "No vaccine yet",
    noCheckYet: "No check yet",
    oneYear: "1 year",
    multipleYears: "{{age}} years",
    animalWeight: "Weight",
    animalTagId: "Tag ID",
    nextCheck: "Next Health Check",
    notScheduled: "Not scheduled",
    overdue: "Overdue",
    tomorrow: "Tomorrow",
    inDays: "In {{days}} days",
    scheduleCheck: "Schedule Check",
    performCheckNow: "Perform Check Now",
    selectedAnimal: "Selected Animal",
    selectAnimal: "Select Animal",
    cow: "Cow",
    goat: "Goat",
    poultry: "Poultry",
    fish: "Fish",
    dog: "Dog",
    cat: "Cat",
    sheep: "Sheep",
    horse: "Horse",
    rabbit: "Rabbit",
    camel: "Camel",
    buffalo: "Buffalo",
    donkey: "Donkey",
    pig: "Pig",
    type: "Animal Type",
    photoRequired: "Photo is required",
    photoAdded: "Photo added",
    analyzeWithAI: "Analyze with AI",
    apiKeyMissing: "OpenAI API key is missing",
    analyzingImage: "",
    breedRequired: "Breed is required",
    ageRequired: "Age is required",
    weightRequired: "Weight is required",
    tagIdRequired: "Tag ID is required",
    loginRequired: "You must be logged in to add an animal",
    selectAnimalRequired: "Please select an animal",
    addSuccess: "Animal added successfully",
    addNewBreed: "Add new breed",
    enterNewBreed: "Enter new breed",
    add: "Add",
    searchSpecific: "Search specific",
    searchAnimals: "Search Animals",
    updateSuccess: "Record updated successfully",
    deleteTitle: "Delete Animal",
    deleteConfirmation: "Are you sure you want to delete this animal? This action cannot be undone.",
    noAnimalsFound: "No Animals Found",
    addAnimalButton:"Add Animal",
    noAnimalsYet: "No Animals Yet",
    tryDifferentSearch: "Try Different Search",
    addFirstAnimal: "Add your first animal to start monitoring your animals' health",
    animal: "Animal",
    loading: "Loading Animals...",
    "father": "Father",
    "mother": "Mother",
    "selectFatherPlaceholder": "Select Father or Origin",
    "selectMotherPlaceholder": "Select Mother or Origin",
    "boughtFromExternal": "Bought from External",
    "children": "Children",
    "noFamilyInfo": "No family information available.",
    familyTree: "Family Tree",
    loadingDetails: "Loading animal details...",
  },

  records: {
    title: "Health Records",
    all: "All",
    allRecords: "All Records",
    addRecord: "Add Record",
    editRecord: "Edit Record",
    recordDetails: "Record Details",
    date: "Date",
    symptoms: "Symptoms",
    diagnosis: "Diagnosis",
    treatment: "Treatment",
    medication: "Medication",
    dosage: "Dosage",
    duration: "Duration",
    notes: "Notes",
    addNotes: "Add Notes",
    followUp: "Follow Up",
    cost: "Cost",
    attachments: "Attachments",
    veterinarian: "Veterinarian",
    clinic: "Clinic",
    deleteConfirm: "Are you sure you want to delete this record?",
    deleteSuccess: "Record deleted successfully",
    noRecords: "No health records found",
    addRecordsMessage: "Add health records to track medical history",
    selectSymptoms: "Select Symptoms",
    confirmedDisease: "Confirmed Disease",
    suspectedDiseases: "Suspected Diseases",
    recordType: "Record Type",
    recordTypeRequired: "Please select a record type",
    selectRecordType: "Select Record Type",
    searchRecordTypes: "Search Record Types",
    searchAnimals: "Search Animals",
    saveRecord: "Save Record",
    medicationName: "Medication Name",
    medicationLabel: "Medication",
    selectMedicationDetails: "Select Medication Details",
    searchMedicationDetails: "Search Medication Details",
    filterRecords: "Filter Records",
    filterHealthRecords: "Filter Health Records",
    showAllRecords: "Show all health records",
    vaccination: "Vaccination",
    vaccinations: "Vaccinations",
    vaccinationsDescription: "Track immunizations and boosters",
    vaccineName: "Vaccine Name",
    medications: "Medications",
    medicationsDescription: "Record treatments and medicines",
    medicineName: "Medicine Name",
    surgery: "Surgery",
    surgeries: "Surgeries",
    surgeriesDescription: "Track surgical procedures",
    surgeryType: "Surgery Type",
    checkup: "Checkup",
    checkups: "Checkups",
    checkupsDescription: "Regular health examinations",
    checkupType: "Checkup Type",
    births: "Births",
    birthsDescription: "Record birth events",
    birthType: "Birth Type",
    healthRecord: "Health Record",
    details: "Details",
    nextVaccinationDue: "Next vaccination due",
    severity: {
      low: "Low",
      medium: "Medium",
      high: "High",
    },
    zoonotic: "Zoonotic",
    contagious: "Contagious",
    practitionerPlaceholder: "Enter practitioner name",
    practitioner: "Practitioner",
    addNew: "Add new {{type}}",
    vaccine: "vaccine",
    medicine: "medicine",
    selectBirthDetails: "Select birth details",
    recording: "Recording...",
    recordingStarted: "Voice recording started",
    recordingStopped: "Voice recording stopped",
    recordingFailed: "Failed to start recording",
    nextCheckupDue: "Next Checkup Due",
    selectSurergyDetails: "Select surgery details",
    selectVaccinationDetails: "Select vaccination details",
    selectGeneralDetails: "Select checkup details",
    searchSurgeries: "Search surgeries",
    searchVaccinations: "Search vaccinations",
    searchMedications: "Search medications",
    searchCheckups: "Search checkups",
    searchBirths: "Search births",
    "detailsPrefix:": "Details:", // Correct
    "practitionerPrefix": "Practitioner:", // Correct
    "nextVaccinationDuePrefix": "Next Vaccination Due:", // Correct
    "nextCheckupDuePrefix": "Next Checkup Due:", // Correct
    loadingDetails: "Loading record details...",

  },

  healthChecks: {
    initialCheckOverdue: "Initial check overdue",
    title: "Health Checks",
    all: "All",
    addCheck: "Add Health Check",
    editCheck: "Edit Health Check",
    checkDetails: "Health Check Details",
    date: "Date",
    temperature: "Temperature",
    weight: "Weight",
    appetite: "Appetite",
    hydration: "Hydration",
    respiration: "Respiration",
    gait: "Gait/Movement",
    fecal: "Fecal Condition",
    coat: "Coat & Skin",
    eyes: "Eyes & Nose",
    ears: "Ears",
    notes: "Notes",
    addNotes: "Add Notes",
    abnormalities: "Abnormalities",
    abnormalitiesDetected: "Abnormalities Detected",
    normal: "Normal",
    abnormal: "Abnormal",
    increased: "Increased",
    decreased: "Decreased",
    none: "None",
    dehydrated: "Dehydrated",
    overhydrated: "Overhydrated",
    labored: "Labored",
    limping: "Limping",
    stiff: "Stiff",
    unableToMove: "Unable to Move",
    diarrhea: "Diarrhea",
    constipated: "Constipated",
    bloody: "Bloody",
    dull: "Dull",
    patchy: "Patchy",
    irritated: "Irritated",
    discharge: "Discharge",
    cloudy: "Cloudy",
    red: "Red",
    swollen: "Swollen",
    enterTemperature: "Enter Temperature",
    enterWeight: "Enter Weight",
    deleteConfirm: "Are you sure you want to delete this health check?",
    deleteSuccess: "Health check deleted successfully",
    noChecks: "No health checks found",
    performRegularChecks: "Perform regular health checks to monitor animal health",
    performCheck: "Perform Health Check",
    nextCheckDate: "Next Check Date",
    saveHealthCheck: "Save Health Check",
    loadingDetails: "Loading health check details...",
    notFound: "Health check not found",
    notFoundMessage: "The health check you're looking for doesn't exist or has been deleted.",
    vitalSigns: "Vital Signs",
    physicalCondition: "Physical Condition",
    abnormalitiesWarning: "Please note any abnormal conditions in the notes section.",
    notRecorded: "Not Recorded",
    scheduleNextCheck: "Schedule Next Check",
    nextHealthCheck: "Next Health Check",
    types: {
      temperature: "Temperature",
      appetite: "Appetite",
      hydration: "Hydration",
      breathing: "Breathing",
      gait: "Movement",
      fecal: "Fecal",
      coat: "Coat & Skin",
      eyes: "Eyes & Nose",
      ears: "Ears",
      weight: "Weight",
    },
    retryAnalysis: "Retry Analysis",
  },

  alerts: {
    title: "Health Alerts",
    noAlerts: "No health alerts",
    allClear: "All animals appear to be healthy",
    abnormalHealthCheck: "Abnormal Health Check",
    highSeverityDisease: "High Severity Disease",
    zoonoticDisease: "Zoonotic Disease",
    contagiousDisease: "Contagious Disease",
    overdueHealthCheck: "Overdue Health Check",
    alertsRequiringAttention: "Alerts requiring attention",
    tapToTakeAction: "Tap to take action",
    hasHealthAbnormalities: "Has health abnormalities",
  },

  settings: {
    title: "Settings",
    account: "Account",
    accountSettings: "Account Settings",
    accountSettingsDescription: "Manage your account information",
    profile: "Profile",
    profileDescription: "View your profile information",
    language: "Language",
    languages: {
      en: "English",
      ur: "Urdu",
      pa: "Punjabi",
    },
    appearance: "Appearance",
    preferences: "Preferences",
    aiFeatures: "AI Features",
    about: "About",
    healthTools: "Health Tools",
    textSize: "Text Size",
    textSizeSmall: "Small",
    textSizeMedium: "Medium",
    textSizeLarge: "Large",
    textSizeDescription: "Adjust the text size for better readability",
    darkMode: "Dark Mode",
    darkModeDescription: "Use dark theme for low light conditions",
    sound: "Sound",
    soundDescription: "Enable sound effects and audio feedback",
    aiVisionAnalysis: "AI Vision Analysis",
    aiVisionDescription: "Use AI to analyze animal images and detect health issues",
    aboutApp: "About App",
    aboutAppDescription: "Learn more about Livestock Tracker",
    symptomsChecker: "Symptoms Checker",
    symptomsCheckerDescription: "Identify potential health issues based on symptoms",
    logout: "Logout",
    logoutTitle: "Confirm Logout",
    logoutMessage: "Are you sure you want to logout?",
  },
  symptoms: {
    title: 'Symptoms Checker',
    search: 'Search symptoms',
    selectedSymptomsTitle: 'Selected Symptoms',
    selectedSymptomsCount: 'Selected Symptoms ({{count}})',
    possibleConditionsTitle: 'Possible Conditions',
    matchingSymptomsPrefix: 'Matching symptoms: ',
    allSymptomsTitle: 'All Symptoms',
    noSymptomsFound: 'No symptoms found',
    // Symptoms from data/symptoms.ts
    symptom_s1_name: "Fever",
    symptom_s1_description: "Elevated body temperature above normal range",
    symptom_s2_name: "Coughing",
    symptom_s2_description: "Forceful expulsion of air from the lungs",
    symptom_s3_name: "Lethargy",
    symptom_s3_description: "Lack of energy, unusual tiredness",
    symptom_s4_name: "Diarrhea",
    symptom_s4_description: "Loose, watery stools occurring more frequently than normal",
    symptom_s5_name: "Vomiting",
    symptom_s5_description: "Forceful expulsion of stomach contents through the mouth",
    symptom_s6_name: "Nasal discharge",
    symptom_s6_description: "Fluid coming from the nose",
    symptom_s7_name: "Lameness",
    symptom_s7_description: "Difficulty walking or standing, limping",
    symptom_s8_name: "Weight loss",
    symptom_s8_description: "Unintended decrease in body weight",

    // Diseases from data/symptoms.ts
    disease_d1_name: "Foot and Mouth Disease",
    disease_d1_description: "Highly contagious viral disease affecting cloven-hoofed animals",
    disease_d1_treatment: "Supportive care, anti-inflammatory medication",
    disease_d1_prevention: "Vaccination, biosecurity measures",

    disease_d2_name: "Bovine Respiratory Disease",
    disease_d2_description: "Complex of respiratory diseases affecting cattle",
    disease_d2_treatment: "Antibiotics, anti-inflammatory drugs",
    disease_d2_prevention: "Vaccination, proper ventilation, stress reduction",

    disease_d3_name: "Mastitis",
    disease_d3_description: "Inflammation of the mammary gland in dairy animals",
    disease_d3_treatment: "Antibiotics, frequent milking, supportive care",
    disease_d3_prevention: "Proper milking hygiene, regular udder checks",

    disease_d4_name: "Brucellosis",
    disease_d4_description: "Bacterial infection affecting multiple species",
    disease_d4_treatment: "Antibiotics, supportive care",
    disease_d4_prevention: "Vaccination, testing and culling infected animals",

  },
  tabs: {
    dashboard: "Dashboard",
    animals: "Animals",
    farms: "Farms",
    expenses: "Expenses",
    settings: "Settings",
    tasks: "Tasks",
    pregnancy: "Pregnancies",
  },


  account: {
    updateAccount: "Update Account",
    updateEmail: "Update Email",
    updatePassword: "Update Password",
    currentEmail: "Current Email",
    newEmail: "New Email",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmNewPassword: "Confirm New Password",
    saveChanges: "Save Changes",
    emailUpdated: "Email updated successfully",
    passwordUpdated: "Password updated successfully",
    updateFailed: "Update failed. Please try again.",
    passwordsDontMatch: "New passwords don't match",
    passwordTooShort: "Password must be at least 6 characters",
    invalidEmail: "Please enter a valid email address",
  },

  profile: {
    title: "Profile",
    personalInfo: "Personal Information",
    accountInfo: "Account Information",
    statistics: "Statistics",
    name: "Name",
    email: "Email",
    role: "Role",
    memberSince: "Member Since",
    emailVerified: "Email Verified",
    verified: "Verified",
    notVerified: "Not Verified",
    farmsOwned: "Farms Owned",
    farmsAssigned: "Farms Assigned",
    totalAnimals: "Total Animals",
    profilePicture: "Profile Picture",
    changeProfilePicture: "Change Profile Picture",
    removeProfilePicture: "Remove Profile Picture",
    selectImageSource: "Select Image Source",
    camera: "Camera",
    gallery: "Gallery",
    roles: {
      owner: "Owner",
      admin: "Administrator",
      caretaker: "Caretaker",
    },
  },

  language: {
    selectLanguage: "Select Language",
    english: "English",
    urdu: "Urdu",
    punjabi: "Punjabi",
  },

  about: {
    appName: "Livestock Tracker",
    version: "Version",
    aboutTitle: "About",
    description: "Livestock Tracker is a comprehensive app designed to help farmers and livestock owners manage their animals efficiently. Track health records, schedule check-ups, and monitor the wellbeing of your livestock all in one place.",
    featuresTitle: "Features",
    featureHealthTracking: "Health tracking and monitoring",
    featureSymptomId: "Symptom identification",
    featureOfflineAccess: "Offline access to critical data",
    featureLanguageSupport: "Multiple language support",
    contactTitle: "Contact",
    legalTitle: "Legal",
    copyright: "Livestock Tracker. All rights reserved.",
  },
  farms: {
    farm: "Farm",
    expenses: "Expenses",
    expenseAnalytics: " Analytics",
    title: "Farms",
    addFarm: "Add Farm",
    editFarm: "Edit Farm",
    farmDetails: "Farm Details",
    name: "Farm Name",
    namePlaceholder: "Enter farm name",
    nameRequired: "Farm name is required",
    location: "Location",
    locationPlaceholder: "Enter location",
    locationRequired: "Location is required",
    status: "Status",
    statusActive: "Active",
    statusInactive: "Inactive",
    statusCompleted: "Completed",
    statusPending: "Pending",
    statusActiveDescription: "Farms that are currently active and in use",
    statusInactiveDescription: "Farms that are no longer in use",
    statusCompletedDescription: "Farms that have been completed",
    statusPendingDescription: "Farms waiting for approval or action",
    animals: "Animals",
    staff: "Staff",
    milking: "Milking",
    noFarms: "No farms found",
    addYourFirstFarm: "Add your first farm to get started",
    saveFarm: "Save Farm",
    addError: "Failed to add farm. Please try again.",
    addSuccess: "Farm added successfully",
    deleteConfirm: "Are you sure you want to delete this farm?",
    deleteSuccess: "Farm deleted successfully",
    viewAnimals: "View Animals",
    addEmployee: "Add Employee",
    assignTask: "Assign Task",
    filterFarms: "Filter Farms",
    filterByStatus: "Filter by Status",
    searchFarms: "Search Farms",
    farmName: "Farm Name",
    farmNamePlaceholder: "Select a farm",
    farmNameRequired: "Farm name is required",
    requiredTitle: "Farm Required",
    requiredMessage: "You need to create a farm before accessing other features. Please add a farm first.",
    updateSuccess: "Farm updated successfully",
    updateError: "Failed to update farm. Please try again.",
    noStaff: "No staff found",
    addYourFirstStaff: "Add your first staff member to get started",
    selectStatus: "Select Status",
    selectFarm: "Select Farm",
    selectFarmPlaceholder: "Select Farm",
    allFarms: "All Farms",
    showAllFarms: "Show statistics for all farms",
    selectFarms: "Select Farms",
    selectFarmsPlaceholder: "Select one or more farms",
    tasks: {
      title: "Tasks",
      addTask: "Add Task",
      editTask: "Edit Task",
      taskDetails: "Task Details",
      taskName: "Task Name",
      taskNamePlaceholder: "Enter task name",
      taskNameRequired: "Task name is required",
      description: "Description",
      descriptionPlaceholder: "Enter task description",
      assignedTo: "Assigned To",
      assignedToPlaceholder: "Select staff member",
      assignedToRequired: "Assigned staff is required",
      dueDate: "Due Date",
      dueDateRequired: "Due date is required",
      status: "Status",
      statusTodo: "To Do",
      statusInProgress: "In Progress",
      statusCompleted: "Completed",
      statusOverdue: "Overdue",
      saveTask: "Save Task",
      addError: "Failed to add task. Please try again.",
      addSuccess: "Task added successfully",
      deleteConfirm: "Are you sure you want to delete this task?",
      deleteSuccess: "Task deleted successfully",
      noTasks: "No tasks found",
      addYourFirstTask: "Add your first task to get started",
      recurrence: "Recurrence",
      selectRecurrence: "Select Recurrence",
      selectRecurrencePlaceholder: "Select how often this task repeats",
      recurrenceValues: {
        none: "One-time task",
        daily: "Daily",
        weekly: "Weekly",
        monthly: "Monthly"
      },
      recurringTask: "Recurring Task",
      nextOccurrence: "Next occurrence will be created automatically when completed",
    },
    staffSection: {
      loading:"Loading Staff...",
      title: "Staff",
      addStaff: "Add Staff Member",
      editStaff: "Edit Staff Member",
      staffDetails: "Staff Details",
      name: "Name",
      namePlaceholder: "Enter staff name",
      nameRequired: "Staff name is required",
      role: "Role",
      rolePlaceholder: "Enter staff role",
      roleRequired: "Staff role is required",
      contact: "Contact",
      contactPlaceholder: "Enter contact information",
      contactRequired: "Contact information is required",
      saveStaff: "Save Staff Member",
      addError: "Failed to add staff member. Please try again.",
      addSuccess: "Staff member added successfully",
      deleteConfirm: "Are you sure you want to delete this staff member?",
      deleteSuccess: "Staff member deleted successfully",
      noStaff: "No staff members found",
      addYourFirstStaff: "Add your first staff member to get started",
      admin: "Admin",
      caretaker: "Caretaker",
      joined: "Joined",
      addFirstStaff: "Add your first staff member to get started",
      joiningDate: "Joining Date",
      employeeDetails: "Employee Details",
      cnic: "CNIC",
      cnicPlaceholder: "Enter CNIC",
      cnicRequired: "CNIC is required",
      photo: "Photo",
      addEmployee: "Add Employee",
      gender: "Gender",
      genderPlaceholder: "Select gender",
      genderRequired: "Gender is required",
      status: "Status",
      statusActive: "Active",
      statusInactive: "Inactive",
      roles: {
        admin: "Admin",
        caretaker: "Caretaker",
        owner: "Owner"
      },
      addPhoto: "Add Photo",
      takePhoto: "Take Photo",
      choosePhoto: "Choose from Library",
      saveEmployee: "Save Employee",
      updateSuccess: "Staff member updated successfully",
      updateError: "Failed to update staff member. Please try again.",
    },
    totalExpenses: "Total Expenses",
    accessibleFarms: "Accessible Farms",
    noAssignedFarms: "This employee has no accessible farms",
    address: "Address",
    expensesByCategory: "Expenses By Category",
    expenseDetails: "Expense Details",
    loading:"Loading Farms..."
  },
  expenses: {
    title: "Expenses",
    noExpenses: "No expenses",
    addYourFirstExpense: "Add your first expense to track costs",
    addExpense: "Add Expense",
    addNew: "Add New Expense",
    editExpense: "Edit Expense",
    expenseDetails: "Expense Details",
    amount: "Amount",
    amountPlaceholder: "Enter amount",
    pickImage: "Pick image",
    takePhoto: "Take Photo",
    date: "Date",
    receipt: "Receipt",
    addReceipt: "Add Receipt",
    receiptAdded: "Receipt added",
    analyzing: "Analyzing receipt...",
    apiKeyMissing: "OpenAI API key is missing. Please add it in settings to use receipt analysis.",
    receiptAnalysisError: "Failed to analyze receipt. Please try again or fill the form manually.",
    photoError: "Error taking photo",
    imagePickError: "Error selecting image",
    optional: "Optional",
    selectCurrency: "Select Currency",
    selectStaff: "Select Staff",
    selectStaffPlaceholder: "Select staff member",
    searchStaff: "Search staff",
    updateExpense: "Update Expense",
    allCategories: "All Categories",
    selectMonth: "Select Month",
    monthlyOverview: "Monthly Overview",
    errors: {
      invalidAmount: "Please enter a valid amount",
      categoryRequired: "Please select a category",
      farmRequired: "Please select a farm",
      paymentMethodRequired: "Please select a payment method",
      staffRequired: "Please select a staff member"
    },
    category: {
      label: "Category",
      select: "Select category",
      animalpurchase: "Animal Purchase",
      feed: "Feed",
      medication: "Medication",
      vaccination: "Vaccination",
      veterinary: "Veterinary Services",
      equipment: "Equipment",
      utilities: "Utilities",
      labor: "Labor",
      maintenance: "Maintenance",
      other: "Other"
    },
    paymentMethod: {
      label: "Payment Method",
      select: "Select payment method",
      cash: "Cash",
      card: "Card",
      bankTransfer: "Bank Transfer",
      other: "Other"
    },
    validation: {
      amount: "Please enter a valid amount",
      category: "Please select a category",
      farm: "Please select a farm",
      paymentMethod: "Please select a payment method"
    },
    descriptionPlaceholder: "Enter description or notes about this expense",
    description: "Description",
    filterByFarm: "Filter by farm",
    loading: "Loading expenses...",
    addSuccess: "Expense Added",
    addSuccessDetail: "Expense has been added successfully",
    addError: "Failed to add expense. Please try again.",
    updateSuccess: "Expense Updated",
    updateSuccessDetail: "Expense has been updated successfully",
    updateError: "Failed to update expense. Please try again.",
    deleteSuccess: "Expense Deleted",
    deleteSuccessDetail: "Expense has been deleted successfully",
    deleteError: "Failed to delete expense. Please try again.",
    deleteConfirmation: "Are you sure you want to delete this expense?",
    deleteTitle: "Delete Expense",
    totalExpenses: "Total Expenses",
    recentExpenses: "Recent Expenses",
    viewAll: "View All",
    expenseBreakdown: "Expense Breakdown",
    byCategory: "Expenses by Category",
    byFarm: "By Farm",
    dateRange: "Date Range",
    thisMonth: "This Month",
    lastMonth: "Last Month",
    last3Months: "Last 3 Months",
    last6Months: "Last 6 Months",
    thisYear: "This Year",
    custom: "Custom",
    from: "From",
    to: "To",
    apply: "Apply",
    reset: "Reset",
    currency: "$",
    total: "Total",
    average: "Average",
    highest: "Highest",
    lowest: "Lowest",
    noExpensesInRange: "No expenses in selected date range",
    exportData: "Export Data",
    importData: "Import Data",
    exportSuccess: "Data exported successfully",
    importSuccess: "Data imported successfully",
    exportError: "Failed to export data",
    importError: "Failed to import data",
    selectCategory: "Select Category",
    totalSpent: "Total Spent",
    totalEntries: "Total Entries"
  },
  farm: {
    select: "Select Farm",
    selectPlaceholder: "Select a farm"
  },
  animal: {
    select: "Select Animal",
    selectPlaceholder: "Select an animal (optional)"
  },
  users: {
    notFoundMessage: "User not found",
    notFound: "User Not Found",
  },
  tasks: {
    loading:"Loading Tasks...",
    title: "Tasks",
    myTasks: "My Tasks",
    tasks: "Tasks",
    manage: "Manage",
    addTask: "Add Task",
    taskDetails: "Task Details",
    all: "All",
    pending: "Pending",
    completed: "Completed",
    overdue: "Overdue",
    inProgress: "In Progress",
    noTasks: "No Tasks",
    noTasksMessage: "You don't have any tasks assigned to you yet.",
    titlePlaceholder: "Enter task title",
    description: "Description",
    descriptionPlaceholder: "Enter task description",
    dueDate: "Due Date",
    priority: "Priority",
    priorityValue: {
      low: "Low",
      medium: "Medium",
      high: "High"
    },
    selectPriority: "Select Priority",
    selectPriorityPlaceholder: "Select priority level",
    searchPriorities: 'Search Priorities',
    status: "Status",
    assignTo: "Assign To",
    assignedTo: "Assigned To",
    selectAssignee: "Select Assignee",
    selectAssigneePlaceholder: "Select a staff member",
    searchEmployees: "Search employees",
    notes: "Notes",
    notesPlaceholder: "Add any additional notes",
    details: "Task Details",
    markAsComplete: "Mark as Complete",
    markComplete: "Complete Task",
    markCompleteConfirm: "Are you sure you want to mark this task as complete?",
    deleteTask: "Delete Task",
    deleteConfirm: "Are you sure you want to delete this task? This action cannot be undone.",
    deleted: "Task deleted successfully",
    markedComplete: "Task marked as complete",
    addSuccess: "Task added successfully",
    notFound: "Task not found",
    errors: {
      titleRequired: "Title is required",
      farmRequired: "Farm is required",
      assigneeRequired: "Assignee is required"
    },
    selectFarmFirst: "Please select a farm first",
    noEmployeesInFarm: "No employees found in this farm. Add employees to the farm first.",
    recurrence: "Recurrence",
    selectRecurrence: "Select Recurrence",
    selectRecurrencePlaceholder: "How often should this task repeat?",
    recurrenceValues: {
      none: "None",
      daily: "Daily",
      weekly: "Weekly",
      monthly: "Monthly"
    },
    farm: "Farm",
    recurrences: "Recurrences",
    searchRecurrences: "Search Recurrences"
  },
  milking: {
    loading:"Loading Milking Records...",
    title: "Milking Records",
    addRecord: "Add Milking Record",
    editRecord: "Edit Milking Record",
    recordDetails: "Milking Record Details",
    noRecords: "No milking records",
    addYourFirstRecord: "Add your first milking record to track production",
    noRecordsForFilter: "No records found for selected filters",
    tryDifferentFilters: "Try adjusting your filters or add new milking records",
    averagePerRecord: "Average per Record",
    recentRecords: "Recent Records",
    statistics: "Milking Statistics",
    totalProduction: "Total Production",
    dailyAverage: "Daily Average",
    monthlyTrend: "Monthly Trend",
    liters: "Liters",
    records: "Records",
    quantity: "Quantity (L)",
    quality: "Quality",
    session: "Session",
    sessions: {
      morning: "Morning",
      afternoon: "Afternoon",
      evening: "Evening"
    },
    qualities: {
      excellent: "Excellent",
      good: "Good",
      fair: "Fair",
      poor: "Poor"
    },
    animal: "Animal",
    date: "Date",
    notes: "Notes",
    milkedBy: "Milked By",
    temperature: "Temperature (°C)",
    fat: "Fat %",
    protein: "Protein %",
    addSuccess: "Milking record added successfully",
    updateSuccess: "Milking record updated successfully",
    deleteSuccess: "Milking record deleted successfully",
    deleteConfirm: "Are you sure you want to delete this milking record?",
    errors: {
      animalRequired: "Animal is required",
      quantityRequired: "Quantity is required",
      qualityRequired: "Quality is required",
      sessionRequired: "Session is required",
      dateRequired: "Date is required",
      invalidFat: "Please enter a valid fat percentage",
      invalidProtein: "Please enter a valid protein percentage",
      invalidTemperature: "Please enter a valid temperature"
    }
  },
  staff: {
    loading:"Loading Staff...",
    title: "Staff",
    assignStaff: "Staff",
    addStaff: "Add Staff",
    noStaff: "No Staff",
    addStaffMembers: "Add staff members to assign them tasks",
    assign: "Assign",
    // other staff translations
  },
  validation: {
    required: "{{field}} is required",
    invalidEmail: "Please enter a valid email address",
    invalidNumber: "Please enter a valid number for {{field}}",
    invalidPassword: "Password must be at least 6 characters long",
    passwordMismatch: "Passwords do not match",
    invalidPhone: "Please enter a valid phone number",
    invalidCNIC: "Please enter a valid CNIC number",
    photoUploadError: "Failed to upload photo. Please try again.",
    photoUploadSkipped: "Could not upload photo. Continuing without photo.",
    photoSelectionError: "Failed to select photo. Please try again.",
    invalidDate: "Please enter a valid date",
    invalidTime: "Please enter a valid time",
    invalidAmount: "Please enter a valid amount",
    invalidWeight: "Please enter a valid weight",
    invalidAge: "Please enter a valid age",
    invalidName: "Please enter a valid name",
    invalidDescription: "Please enter a valid description",
    invalidQuantity: "Please enter a valid quantity"
  },
  pregnancy: {
    loading:"Loading Pregnancies...",
    title: "Pregnancies",
    expectedDate: "Expected Date",
    daysRemaining: "Days Remaining",
    stage: "Stage",
    progress: "Progress",
    fullReport: "Full Report",
    dietPlan: "Diet Plan",
    treatment: "Treatment",
    calendar: "Calendar",
    addPregnancy: "Add Pregnancy",
    report: "Report",
    dietTab: "Diet",
    healthTab: "Health",
    details: "Pregnancy Details",
    timeline: "Timeline",
    animal: "Animal",
    sire: "Sire",
    species: "Species",
    conceptionDate: "Conception Date",
    dietRecommendations: "Diet Recommendations",
    dietSummary: "This {{species}} pregnancy is currently in week {{currentWeek}} of approximately {{totalWeeks}} weeks.",
    healthChecks: "Health Checks",
    healthSummary: "This {{species}} pregnancy is currently in week {{currentWeek}} of approximately {{totalWeeks}} weeks.",
    aiGenerated: "AI Generated",
    stages: {
      early: "Early Stage",
      mid: "Middle Stage",
      late: "Late Stage"
    },
    noPregnancies: "No pregnancies recorded yet.",
    noPregnanciesMessage: "Add a pregnancy record to track animal pregnancies.",
    addTitle: "Add New Pregnancy",
    selectFemaleAnimal: "Select Female Animal",
    selectFemalePlaceholder: "Select female animal",
    selectSire: "Select Sire (Male)",
    selectSirePlaceholder: "Select the sire",
    selectStatusPlaceholder: "Select pregnancy status",
    selectFarm: "Select Farm",
    selectSpecies: "Select Species",
    saveSuccess: "Pregnancy record saved successfully.",
    selectStatus: "Select Status",
    useAIPlans: "Use AI-Generated Plans",
    weekNumber: "Week {{week}}",
    current: "Current",
    selectWeek: "Select Week",
    statusT:"Pregnancy Status",
    searchStatus:"Search Status",
    searchSires:"Search Sires",
    addSuccessMessage:"Pregnancy record added successfully",
    addSuccess:"Pregnancy Added",
    status: {
      active: "Active",
      confirmed: "Confirmed",
      suspected: "Suspected",
      notPregnant: "Not Pregnant"
    },
    statuses: {
      confirmed: "Confirmed",
      suspected: "Suspected",
      not_pregnant: "Not Pregnant"
    },
    totalPregnancies: "Total Pregnancies: {{count}}",
    clickToViewDetails: "Click to view details",
    viewAllPregnancies: "View All Pregnancies",
    health: {
      recommendedCheckups: "Recommended Checkups",
      recommendedTreatments: "Recommended Treatments",
      weekTitle: "Week {{week}}: {{stage}} Health Focus ({{species}})",
      generalDescription: "General health recommendations for a {{species}} in {{stage}} stage, week {{week}}. Monitor well-being and consult a vet for concerns.",
      cow: {
        early: {
          checkup1: "Veterinary confirmation of pregnancy (ultrasound around day 30-60, week {{week}})",
          checkup2: "Body condition scoring",
          treatment1: "Clostridial vaccination (if due, consult vet, week {{week}})",
          treatment2: "Deworming (based on fecal test and vet advice)"
        },
        mid: {
          checkup1: "Monitor for signs of abortion",
          checkup2: "Fetal heart rate check (if equipment available, week {{week}})",
          treatment1: "Booster vaccinations (e.g., ScourGuard, consult vet, week {{week}})",
          treatment2: "Fly control measures"
        },
        late: {
          checkup1: "Check for udder development and colostrum",
          checkup2: "Pelvic ligament relaxation assessment (week {{week}})",
          treatment1: "Vitamin E/Selenium injection (if deficient, 2-3 weeks pre-calving, week {{week}})",
          treatment2: "Ensure clean, dry calving environment"
        }
      },
      goat: {
        early: {
          checkup1: "Pregnancy diagnosis (ultrasound around day 25-45, week {{week}})",
          checkup2: "Parasite load assessment (fecal egg count)",
          treatment1: "CD&T vaccination (if not up-to-date, consult vet, week {{week}})",
          treatment2: "Strategic deworming (if indicated)"
        },
        mid: {
          checkup1: "Monitor for ketosis/pregnancy toxemia signs",
          checkup2: "Body condition check (week {{week}})",
          treatment1: "Booster CD&T vaccination (4 weeks pre-kidding, week {{week}})",
          treatment2: "Lice/mite treatment (if needed)"
        },
        late: {
          checkup1: "Kidding kit preparation check",
          checkup2: "Observation for signs of impending labor (week {{week}})",
          treatment1: "Selenium/Vitamin E supplement (if in deficient area, week {{week}})",
          treatment2: "Prepare kidding supplies (disinfectant, towels)"
        }
      },
      general: {
        checkup1: "General health observation (week {{week}})",
        checkup2: "Consult vet for specific concerns",
        treatment1: "Follow routine health protocols (week {{week}})",
        treatment2: "Administer vet-prescribed treatments only"
      }
    },
    diet: {
      cow: {
        early: {
          title: "Week {{week}}: Foundational Nutrition",
          description: "Focus on quality forage and balanced minerals to support early fetal development.",
          nutrient1: "Calcium and Phosphorus (1:1 ratio)",
          nutrient2: "Vitamin A and E",
          nutrient3: "Quality protein (12-14%)",
          food1: "High-quality hay or pasture (10-15 kg/day)",
          food2: "Balanced mineral mix (100-150g/day)",
          food3: "Limited grain if needed (1-2 kg/day)"
        },
        mid: {
          title: "Week {{week}}: Growth Support",
          description: "Increase energy intake to support growing fetus and maintain body condition.",
          nutrient1: "Increased energy (TDN 60-65%)",
          nutrient2: "Protein (12-14%)",
          nutrient3: "Trace minerals (Selenium, Copper)",
          food1: "Quality forage (12-18 kg/day)",
          food2: "Moderate grain supplementation (2-3 kg/day)",
          food3: "Mineral mix with trace elements (150g/day)"
        },
        late: {
          title: "Week {{week}}: Pre-Calving Preparation",
          description: "Prepare for calving with higher energy diet and proper calcium balance to prevent milk fever.",
          nutrient1: "Higher energy (TDN 65-70%)",
          nutrient2: "Balanced calcium (prevent milk fever)",
          nutrient3: "Vitamin E and Selenium",
          food1: "Energy-dense feed (15-20 kg/day total)",
          food2: "Properly balanced minerals (150-200g/day)",
          food3: "Quality hay with legumes (10-12 kg/day)"
        }
      },
      goat: {
        early: {
          title: "Week {{week}}: Early Kidding Nutrition",
          description: "Provide quality forage and balanced minerals for early fetal development.",
          nutrient1: "Calcium and Phosphorus",
          nutrient2: "Vitamin A",
          nutrient3: "Quality protein (14%)",
          food1: "Browse and forage (2-3 kg/day)",
          food2: "Alfalfa hay (0.5-1 kg/day)",
          food3: "Goat-specific mineral mix (25-30g/day)"
        },
        mid: {
          title: "Week {{week}}: Mid-Gestation Support",
          description: "Gradually increase feed quality to support multiple fetuses common in goats.",
          nutrient1: "Increased energy",
          nutrient2: "Protein (14-16%)",
          nutrient3: "Copper and Selenium",
          food1: "Quality hay (2-3 kg/day)",
          food2: "Limited grain mix (0.5-0.75 kg/day)",
          food3: "Mineral supplement with copper (30g/day)"
        },
        late: {
          title: "Week {{week}}: Late Gestation Preparation",
          description: "Focus on energy-dense nutrition to prevent pregnancy toxemia and support final fetal growth.",
          nutrient1: "Higher energy density",
          nutrient2: "Calcium (prevent toxemia)",
          nutrient3: "Vitamin E",
          food1: "Alfalfa hay (2-3 kg/day)",
          food2: "Balanced grain mix (0.75-1 kg/day)",
          food3: "Molasses as energy source (50-100g/day)"
        }
      },
      sheep: {
        early: {
          title: "Week {{week}}: Early Lambing Nutrition",
          description: "Maintain moderate body condition with quality forage and mineral supplementation.",
          nutrient1: "Selenium",
          nutrient2: "Vitamin E",
          nutrient3: "Quality protein (12-14%)",
          food1: "Quality pasture/hay (1.5-2 kg/day)",
          food2: "Sheep-specific mineral mix (20-25g/day)",
          food3: "Limited grain (0.2-0.4 kg/day)"
        },
        mid: {
          title: "Week {{week}}: Fetal Development Support",
          description: "Gradually increase nutrition to support wool growth and fetal development.",
          nutrient1: "Increased energy",
          nutrient2: "Protein (14%)",
          nutrient3: "Copper (caution with levels)",
          food1: "Mixed hay (2-2.5 kg/day)",
          food2: "Moderate grain (0.4-0.6 kg/day)",
          food3: "Selenium supplement in deficient areas (as prescribed)"
        },
        late: {
          title: "Week {{week}}: Pre-Lambing Preparation",
          description: "Increase energy intake to prevent pregnancy toxemia, especially for ewes carrying multiples.",
          nutrient1: "Higher energy density",
          nutrient2: "Calcium",
          nutrient3: "Vitamin B complex",
          food1: "Energy-dense feed (2.5-3 kg/day total)",
          food2: "Quality hay (1.5-2 kg/day)",
          food3: "Balanced grain mix (0.6-0.8 kg/day)"
        }
      },
      pig: {
        early: {
          title: "Week {{week}}: Early Farrowing Diet",
          description: "Maintain moderate feed intake with balanced protein to support implantation.",
          nutrient1: "Folic acid",
          nutrient2: "Quality protein (12-14%)",
          nutrient3: "Vitamin A",
          food1: "Balanced sow feed (2-2.5 kg/day)",
          food2: "Fresh water (ad libitum)",
          food3: "Limited treats (100-200g/day)"
        },
        mid: {
          title: "Week {{week}}: Fetal Growth Support",
          description: "Gradually increase feed to support growing litter size.",
          nutrient1: "Increased lysine",
          nutrient2: "Calcium and Phosphorus",
          nutrient3: "B vitamins",
          food1: "Increased sow ration (2.5-3 kg/day)",
          food2: "Fresh vegetables (0.5 kg/day)",
          food3: "Clean water (ad libitum)"
        },
        late: {
          title: "Week {{week}}: Pre-Farrowing Preparation",
          description: "Increase feed quality while maintaining fiber to prevent constipation before farrowing.",
          nutrient1: "Fiber",
          nutrient2: "Increased energy",
          nutrient3: "Vitamin E and Selenium",
          food1: "High-fiber feed (3-3.5 kg/day)",
          food2: "Bran supplements (0.2-0.3 kg/day)",
          food3: "Increased feed volume (3.5-4 kg/day total)"
        }
      },
      general: {
        title: "Week {{week}}: {{stage}} Stage Nutrition",
        description: "Focus on balanced nutrition appropriate for this stage of pregnancy.",
        nutrient1: "Balanced minerals",
        nutrient2: "Quality protein",
        nutrient3: "Essential vitamins",
        food1: "Quality forage (adjust to body weight)",
        food2: "Balanced feed mix (adjust to body weight)",
        food3: "Fresh water (ad libitum)"
      },
      keyNutrients: "Key Nutrients",
      recommendedFoods: "Recommended Foods"
    },

    // Validation messages for pregnancy form
    farmRequired: "Farm is required",
    femaleAnimalRequired: "Female animal is required",
    sireRequired: "Sire is required",
    "statusRequired": "Please select pregnancy status.",
  },
  description: "Description",
  descriptionPlaceholder: "Enter task description or tap microphone to record",
  notes: "Notes",
  notesPlaceholder: "Enter additional notes or tap microphone to record",
  "selectFemaleValidation": "Please select a female animal.",
  "selectSireValidation": "Please select a sire (male animal).",
  "conceptionDateRequired": "Please select a conception date.",
  "conceptionDateFuture": "Conception date cannot be in the future.",
  "statusRequired": "Please select pregnancy status.",
  "selectFarmFirst.": "Please select a farm first."
};
