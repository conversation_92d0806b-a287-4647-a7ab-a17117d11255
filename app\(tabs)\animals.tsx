import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Platform,
  Text
} from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from '@/hooks/useTranslation';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useAuthStore } from '@/store/auth-store';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { useRecordStore } from '@/store/record-store';
import AnimalCard from '@/components/AnimalCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { Plus, Cat } from 'lucide-react-native';
import AnimalSearchDropdown from '@/components/AnimalSearchDropdown';
import SpeciesFilterRow from '@/components/SpeciesFilterRow';
// import { LinearGradient } from 'expo-linear-gradient';
// import { animals } from '@/mocks/animals';
export default function AnimalsScreen() {
  const themedColors = useThemeColors();
  const { t, language } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const { animals, fetchAnimals, isLoading: animalsLoading } = useAnimalStore();
  const { fetchHealthChecks, isLoading: healthChecksLoading } = useHealthCheckStore();
  const { fetchRecords, isLoading: recordsLoading } = useRecordStore();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);
  const [selectedAnimalId, setSelectedAnimalId] = useState('');
    // Generate styles dynamically after themedColors is available
  const styles = getStyles(themedColors);

  useEffect(() => {
    let isMounted = true;
    
    const loadData = async () => {
      if (!user) return;
      
      try {
        await Promise.all([
          fetchAnimals(user.id),
          fetchHealthChecks(),
          fetchRecords()
        ]);
      } catch (error) {
        console.error('Error loading animals data:', error);
      }
    };
    
    loadData();
    
    return () => {
      isMounted = false;
    };
  }, [user, fetchAnimals, fetchHealthChecks, fetchRecords]);

  const handleRefresh = async () => {
    setRefreshing(true);
    if (user) {
      try {
        await Promise.all([
          fetchAnimals(user.id),
          fetchHealthChecks(),
          fetchRecords()
        ]);
        setRefreshing(false); // Move this line inside the try block
      } catch (error) {
        console.error('Error refreshing animals data:', error);
      } finally {
        setRefreshing(false);
      }
    } else {
      setRefreshing(false);
    }
  };

  const handleAddAnimal = () => {
    router.push('/animals/add');
  };

  const handleAnimalSelect = (animalId: string) => {
    setSelectedAnimalId(animalId);
    // If an animal is selected, clear the species filter
    if (animalId) {
      setSelectedSpecies(null);
    }
  };

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    // If user types in search box, clear the selected animal
    if (text && selectedAnimalId) {
      setSelectedAnimalId('');
    }
  };

  const handleSpeciesFilter = (species: string | null) => {
    setSelectedSpecies(species);
  };

  // Add this useEffect to reset species filter if no animals match
  useEffect(() => {
    if (selectedSpecies) {
      const matchingAnimals = animals.filter(
        animal => animal.species.toLowerCase() === selectedSpecies.toLowerCase()
      );
      
      if (matchingAnimals.length === 0) {
        // No animals match the selected species
        // Optionally reset the species filter or show a message
        // setSelectedSpecies(null);
      }
    }
  }, [selectedSpecies, animals]);

  // Use case-insensitive comparison for species filtering
  const filteredAnimals = animals.filter(animal => {
    // If a specific animal is selected, only show that animal
    if (selectedAnimalId) {
      return animal.id === selectedAnimalId;
    }

    // Apply search filter
    const matchesSearch = searchQuery
      ? animal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        animal.species.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (animal.breed && animal.breed.toLowerCase().includes(searchQuery.toLowerCase()))
      : true;

    // Apply species filter with case-insensitive comparison
    const matchesSpecies = selectedSpecies 
      ? animal.species.toLowerCase() === selectedSpecies.toLowerCase() 
      : true;

    return matchesSearch && matchesSpecies;
  });

  // const isLoading = animalsLoading || healthChecksLoading || recordsLoading;
  const isLoading =  healthChecksLoading || recordsLoading;

  if (isLoading && !refreshing) {
    return <LoadingIndicator fullScreen message={t('animals.loading')} />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors.background }]} edges={['bottom']}>

      {/* Species filter row */}
      <View style={[styles.filterContainer, { backgroundColor: themedColors.background }]}>
      <Text  style={[styles.animalSpecies, { color: themedColors.textSecondary }, language === 'ur' ? styles.urduText : null]}>{t('animals.animalSpecies')}</Text>
      <SpeciesFilterRow
          selectedSpecies={selectedSpecies}
          onSelectSpecies={handleSpeciesFilter}
        />
      </View>
      <View style={styles.header}>
        <AnimalSearchDropdown
          placeholder={selectedSpecies ?
            t('animals.searchSpecific', { species: t(`animals.${selectedSpecies.toLowerCase()}`) || selectedSpecies }) :
            t('animals.searchAnimals')}
          animals={selectedSpecies ? 
            animals.filter(animal => animal.species.toLowerCase() === selectedSpecies.toLowerCase()) : 
            animals}
          value={selectedAnimalId}
          onSelect={handleAnimalSelect}
          onChangeText={handleSearchChange}
        />
      </View>

      {filteredAnimals.length === 0 ? (
        <EmptyState
          title={searchQuery || selectedSpecies ? t('animals.noAnimalsFound') : t('animals.noAnimalsYet')}
          message={searchQuery || selectedSpecies
            ? t('animals.tryDifferentSearch')
            : t('animals.addFirstAnimal')
          }
          actionLabel={searchQuery || selectedSpecies ? undefined : t('animals.addAnimalButton')}
          onAction={searchQuery || selectedSpecies ? undefined : handleAddAnimal}
          icon={<Cat size={48} color={themedColors.primary} />}
          style={styles.emptyState}
        />
      ) : (
        <FlatList
          data={filteredAnimals}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <AnimalCard
              animal={item}
              onPress={() => router.push(`/animals/${item.id}`)}
              showHealthStatus={true}
              hasHealthIssues={item.hasHealthIssues || false}
            />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[themedColors.primary]}
              tintColor={themedColors.primary}
            />
          }
        />
      )}

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.floatingAddButton}
        onPress={handleAddAnimal}
        activeOpacity={0.8}
      >
        <View style={styles.floatingAddButtonContent}>
          <Plus size={24} color="white" />
        </View>
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    gap: 6,
    marginTop: -20,
    zIndex: 100, // Ensure dropdown is above other elements
    elevation: 100, // Android elevation
  },
  filterContainer: {
    paddingHorizontal: 8,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: themedColors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 4,
    fontSize: 14,
    color: themedColors.text,
  },
  addButtonContainer: {
    ...Platform.select({
      ios: {
        shadowColor: '#000', // Use a standard shadow color like black
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
      },
      android: {
        elevation: 3,
      },
    }),
    borderRadius: 20,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },

  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  emptyState: {
    flex: 1,
    paddingTop: 40,
    paddingHorizontal: 16,
  },
  floatingAddButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
    backgroundColor: themedColors.primary,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 10,
  },
  floatingAddButtonContent: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  animalSpecies: {
    fontSize: 14,
    color: themedColors.textSecondary,
    padding:10
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
   
  },
});
