import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  ActivityIndicator,
  StyleSheet,
  Modal,
  FlatList,
  TextInput,
  Platform
} from 'react-native';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '@/store/auth-store';
import { useExpenseStore } from '@/store/expense-store';
import { Expense, ExpenseCategory, PaymentMethod } from '@/types/expense';
import * as ImagePicker from 'expo-image-picker';
import {
  ArrowLeft,
  Save,
  Calendar,
  DollarSign,
  Tag,
  FileText,
  Camera,
  Image as ImageIcon,
  CreditCard,
  Cat,
  MapPin,
  Banknote,
  Building,
  CircleDollarSign,
  Wheat,
  Pill,
  Syringe,
  Stethoscope,
  Wrench,
  Lightbulb,
  Users,
  Hammer,
  HelpCircle,
  ChevronDown,
  Check,
  CheckCircle2,
  X,
  Grid,
  AlertCircle
} from 'lucide-react-native';
import { colors } from '@/constants/colors';
import Input from '@/components/Input';
import Button from '@/components/Button';
import DatePickerInput from '@/components/DatePickerInput';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';
import { useTranslation } from '@/hooks/useTranslation';
import { useToast } from '@/contexts/ToastContext';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import AnimalSearchDropdown from '@/components/AnimalSearchDropdown';
import { useFarmStore } from '@/store/farm-store';
import { useAnimalStore } from '@/store/animal-store';
import { useSettingsStore } from '@/store/settings-store';
import {
  CURRENCIES,
  CurrencyInfo,
  getPopularCurrencies,
  getAllCurrencies,
  getCurrencyInfo,
  searchCurrencies
} from '@/utils/currency-utils';
import { analyzeReceiptImage } from '@/services/openai-service';

import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { useStaffStore } from '@/store/staff-store';
import { useThemeColors } from '@/hooks/useThemeColors';
import ImageCaptureButtons from '@/components/ImageCaptureButtons';

// Define supported currency types - now supports all currencies from CURRENCIES
type SupportedCurrency = keyof typeof CURRENCIES;

// Define a type for currency options
type CurrencyOption = {
  label: string;
  value: SupportedCurrency;
  info: CurrencyInfo;
};

export default function AddExpenseScreen() {
  const router = useRouter();
  const { user } = useAuthStore();
  const { addExpense, isLoading } = useExpenseStore();
  const { farms, fetchFarms } = useFarmStore();
  const { fetchAnimalsByFarm } = useAnimalStore();
  // Add staff store
  const { staff, fetchStaff } = useStaffStore();
  const { t } = useTranslation();
  const { showToast } = useToast();
  const { playFeedback } = useAudioFeedback();
  const { openaiApiKey } = useSettingsStore();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors);
  const params = useLocalSearchParams<{ farmId: string }>();
  const preselectedFarmId = params.farmId;

  const [amount, setAmount] = useState('');
  const [date, setDate] = useState(new Date());
  const [category, setCategory] = useState<ExpenseCategory | null>(null);
  const [description, setDescription] = useState('');
  const [farmId, setFarmId] = useState<string | null>(null);
  const [farmName, setFarmName] = useState<string | null>(null);
  const [animalId, setAnimalId] = useState<string | null>(null);
  const [animalName, setAnimalName] = useState<string | null>(null);
  const [receiptImage, setReceiptImage] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [currency, setCurrency] = useState<SupportedCurrency>('PKR'); // Default to Pakistani Rupee
  const [showCurrencyPicker, setShowCurrencyPicker] = useState(false);

  const [isAnalyzingReceipt, setIsAnalyzingReceipt] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [descriptionHeight, setDescriptionHeight] = useState(120);
  const [currencySearchQuery, setCurrencySearchQuery] = useState('');


  // Add staff-related state
  const [staffId, setStaffId] = useState<string | null>(null);
  const [staffName, setStaffName] = useState<string | null>(null);

  // Add this function to calculate text height
  const calculateDescriptionHeight = (text: any) => {
    // Approximate height calculation based on text length and line breaks
    const lineHeight = 20; // Approximate line height
    const lines = text.split('\n').length;
    const charsPerLine = 40; // Approximate characters per line
    const additionalLines = Math.ceil(text.length / charsPerLine);

    const totalLines = Math.max(lines, additionalLines);
    const calculatedHeight = Math.min(300, Math.max(120, totalLines * lineHeight));

    setDescriptionHeight(calculatedHeight);
  };

  // Fetch farms when component mounts
  useEffect(() => {
    if (user) {
      fetchFarms(user.id);
    }
  }, [user]);

  // Fetch staff when farm is selected
  useEffect(() => {
    if (farmId && category === ExpenseCategory.LABOR) {
      fetchStaff(farmId);
    }
  }, [farmId, category]);

  // Fetch animals when farm is selected
  useEffect(() => {
    if (farmId) {
      fetchAnimalsByFarm(farmId);
    }
  }, [farmId, fetchAnimalsByFarm]);



  // Handle staff selection
  const handleStaffSelect = (id: string, name: string) => {
    setStaffId(id);
    setStaffName(name);
  };

  // Category options with colorful, distinct icons
  const categoryOptions: DropdownItem[] = [
    {
      id: ExpenseCategory.ANIMAL_PURCHASE,
      label: t('expenses.category.animalPurchase'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#8E44AD' }]}>
        <Cat size={16} color="white" fill="white" />
      </View>
    },
    {
      id: ExpenseCategory.FEED,
      label: t('expenses.category.feed'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#27AE60' }]}>
        <Wheat size={16} color="white" fill="white" />
      </View>
    },
    {
      id: ExpenseCategory.MEDICATION,
      label: t('expenses.category.medication'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#E74C3C' }]}>
        <Pill size={16} color="white" />
      </View>
    },
    {
      id: ExpenseCategory.VACCINATION,
      label: t('expenses.category.vaccination'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#3498DB' }]}>
        <Syringe size={16} color="white" />
      </View>
    },
    {
      id: ExpenseCategory.VETERINARY,
      label: t('expenses.category.veterinary'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#2ECC71' }]}>
        <Stethoscope size={16} color="white" />
      </View>
    },
    {
      id: ExpenseCategory.EQUIPMENT,
      label: t('expenses.category.equipment'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#95A5A6' }]}>
        <Wrench size={16} color="white" />
      </View>
    },
    {
      id: ExpenseCategory.UTILITIES,
      label: t('expenses.category.utilities'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#F39C12' }]}>
        <Lightbulb size={16} color="white" />
      </View>
    },
    {
      id: ExpenseCategory.LABOR,
      label: t('expenses.category.labor'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#1ABC9C' }]}>
        <Users size={16} color="white" />
      </View>
    },
    {
      id: ExpenseCategory.MAINTENANCE,
      label: t('expenses.category.maintenance'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#34495E' }]}>
        <Hammer size={16} color="white" />
      </View>
    },
    {
      id: ExpenseCategory.OTHER,
      label: t('expenses.category.other'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#7F8C8D' }]}>
        <HelpCircle size={16} color="white" />
      </View>
    },
  ];

  // Payment method options with colorful, distinct icons
  const paymentMethodOptions: DropdownItem[] = [
    {
      id: PaymentMethod.CASH,
      label: t('expenses.paymentMethod.cash'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#4CAF50' }]}>
        <Banknote size={16} color="white" />
      </View>
    },
    {
      id: PaymentMethod.CARD,
      label: t('expenses.paymentMethod.card'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#2196F3' }]}>
        <CreditCard size={16} color="white" />
      </View>
    },
    {
      id: PaymentMethod.BANK_TRANSFER,
      label: t('expenses.paymentMethod.bankTransfer'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#9C27B0' }]}>
        <Building size={16} color="white" />
      </View>
    },
    {
      id: PaymentMethod.OTHER,
      label: t('expenses.paymentMethod.other'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: '#FF9800' }]}>
        <CircleDollarSign size={16} color="white" />
      </View>
    },
  ];

  // Handle farm selection
  const handleFarmSelect = (id: string, name: string) => {
    setFarmId(id);
    setFarmName(name);
    // Reset animal selection when farm changes
    setAnimalId(null);
    setAnimalName(null);
  };

  // Handle animal selection
  const handleAnimalSelect = (id: string, name: string) => {
    setAnimalId(id);
    setAnimalName(name);
  };

  // Handle taking a photo
  const handleTakePhoto = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(t('common.permissionRequired'), t('common.cameraPermission'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        setReceiptImage(uri);

        // Analyze receipt and extract data
        await analyzeReceipt(uri);
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('expense.photoError'));
    }
  };

  // Handle picking an image from gallery
  const handlePickImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(t('common.permissionRequired'), t('common.galleryPermission'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        setReceiptImage(uri);

        // Analyze receipt and extract data
        await analyzeReceipt(uri);
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('expense.imagePickError'));
    }
  };

  // Handle receipt analysis
  const analyzeReceipt = async (imageUri: string) => {
    if (!openaiApiKey) {
      setAnalysisError(t('expenses.apiKeyMissing'));
      return;
    }

    try {
      setIsAnalyzingReceipt(true);
      setAnalysisError(null);

      const analysisResult = await analyzeReceiptImage(imageUri, openaiApiKey);

      // Populate form fields with extracted data
      if (analysisResult.amount) setAmount(analysisResult.amount.toString());
      if (analysisResult.date) setDate(new Date(analysisResult.date));
      if (analysisResult.category) setCategory(analysisResult.category as ExpenseCategory);

      // Format items in a tabular structure with proper alignment
      let detailedDescription = '';
      if (analysisResult.items && analysisResult.items.length > 0) {
        const headers = {
          name: 'Item Name',
          quantity: 'Quantity',
          price: 'Price/Unit'
        };

        // Calculate maximum width for each column
        let maxNameWidth = headers.name.length;
        let maxQtyWidth = headers.quantity.length;
        let maxPriceWidth = headers.price.length;
        let totalItems = 0;
        let totalPrice = 0;

        analysisResult.items.forEach((item: any) => {
          const name = item.name || 'Unknown';
          const quantityStr = item.quantity ? item.quantity.toString() : '';
          const pricePerUnitStr = item.pricePerUnit ? item.pricePerUnit.toString() : '';

          if (name.length > maxNameWidth) maxNameWidth = name.length;
          if (quantityStr.length > maxQtyWidth) maxQtyWidth = quantityStr.length;
          if (pricePerUnitStr.length > maxPriceWidth) maxPriceWidth = pricePerUnitStr.length;

          totalItems++;
          if (item.totalPrice) {
            totalPrice += parseFloat(item.totalPrice.toString());
          } else if (item.pricePerUnit && item.quantity) {
            totalPrice += parseFloat(item.pricePerUnit.toString()) * parseFloat(item.quantity.toString());
          }
        });

        const colSpacing = 4; // Increased spacing between columns

        // Helper functions for padding
        const padLeft = (str: string, width: number) => str.padEnd(width, ' '); // For Item Name (left-aligned)
        const padRight = (str: string, width: number) => str.padStart(width, ' '); // For Quantity, Price (right-aligned)

        // Add header (Headers are typically left-aligned)
        detailedDescription += `${padLeft(headers.name, maxNameWidth)}${' '.repeat(colSpacing)}${padLeft(headers.quantity, maxQtyWidth)}${' '.repeat(colSpacing)}${padLeft(headers.price, maxPriceWidth)}\n`;

        // Add separator
        detailedDescription += `${'-'.repeat(maxNameWidth)}${' '.repeat(colSpacing)}${'-'.repeat(maxQtyWidth)}${' '.repeat(colSpacing)}${'-'.repeat(maxPriceWidth)}\n`;

        // Add each item
        analysisResult.items.forEach((item: any) => {
          const name = item.name || 'Unknown';
          const quantityStr = item.quantity ? item.quantity.toString() : '';
          const pricePerUnitStr = item.pricePerUnit ? item.pricePerUnit.toString() : '';

          detailedDescription += `${padLeft(name, maxNameWidth)}${' '.repeat(colSpacing)}${padRight(quantityStr, maxQtyWidth)}${' '.repeat(colSpacing)}${padRight(pricePerUnitStr, maxPriceWidth)}\n`;

          // detailedDescription += `${pad(name, maxNameWidth)}${' '.repeat(colSpacing)}${pad(quantityStr, maxQtyWidth)}${' '.repeat(colSpacing)}${pad(pricePerUnitStr, maxPriceWidth)}\n`;
        });

        // Add totals        
        detailedDescription += `\nTotal Items : ${totalItems}\n`;
        detailedDescription += `Total Price : ${totalPrice.toFixed(2)}\n\n`; // Add extra newline for spacing before shop details
      } else if (analysisResult.description) {
        // Fallback to simple description if no items array
        detailedDescription = analysisResult.description + '\n\n';
      }

      // Add shop details (if any) after the items table
      if (analysisResult.shopName) detailedDescription += `Shop: ${analysisResult.shopName}\n`;
      if (analysisResult.shopContact) detailedDescription += `Contact: ${analysisResult.shopContact}\n`;
      if (analysisResult.shopAddress) detailedDescription += `Address: ${analysisResult.shopAddress}\n`;

      setDescription(detailedDescription.trim());
      if (analysisResult.paymentMethod) setPaymentMethod(analysisResult.paymentMethod as PaymentMethod);

      setIsAnalyzingReceipt(false);
    } catch (error) {
      setIsAnalyzingReceipt(false);
      setAnalysisError(t('expenses.receiptAnalysisError'));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = t('expenses.errors.invalidAmount');
    }

    if (!category) {
      newErrors.category = t('expenses.errors.categoryRequired');
    }

    if (!farmId) {
      newErrors.farmId = t('expenses.errors.farmRequired');
    }

    if (!paymentMethod) {
      newErrors.paymentMethod = t('expenses.errors.paymentMethodRequired');
    }

    // Validate staff selection for Labor category
    if (category === ExpenseCategory.LABOR && !staffId) {
      newErrors.staffId = t('expenses.errors.staffRequired') || "Staff selection is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Get currency options - show popular currencies first, then all others
  const popularCurrencies = getPopularCurrencies();
  const allCurrencies = getAllCurrencies();

  // Filter currencies based on search query
  const filteredCurrencies = currencySearchQuery
    ? searchCurrencies(currencySearchQuery)
    : allCurrencies;

  // Create currency options with popular currencies first (only if no search)
  const currencyOptions: CurrencyOption[] = currencySearchQuery
    ? filteredCurrencies.map(info => ({
        label: `${info.code} (${info.symbol}) - ${info.country}`,
        value: info.code as SupportedCurrency,
        info
      }))
    : [
        // Popular currencies first
        ...popularCurrencies.map(info => ({
          label: `${info.code} (${info.symbol}) - ${info.country}`,
          value: info.code as SupportedCurrency,
          info
        })),
        // Separator
        ...(popularCurrencies.length > 0 ? [{
          label: '─────────────────────',
          value: 'SEPARATOR' as SupportedCurrency,
          info: { code: 'SEPARATOR', symbol: '', name: '', country: '', flag: '' } as CurrencyInfo
        }] : []),
        // All other currencies
        ...allCurrencies
          .filter(info => !popularCurrencies.some(pop => pop.code === info.code))
          .map(info => ({
            label: `${info.code} (${info.symbol}) - ${info.country}`,
            value: info.code as SupportedCurrency,
            info
          }))
      ];

  // Get current currency info
  const currentCurrencyInfo = getCurrencyInfo(currency);



  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      playFeedback('error');
      return;
    }

    if (!user) {
      Alert.alert(t('common.error'), t('common.notLoggedIn'));
      return;
    }

    try {
      // Create a copy of expense data for processing
      let processedExpenseData: any = {
        amount: parseFloat(amount),
        currency: currency as SupportedCurrency,
        date: date.getTime(),
        category: category!,
        description,
        farmId: farmId!, // Make sure this is a valid farm ID, not a user ID
        paymentMethod: paymentMethod!,
        createdBy: user.id, // This is the user ID
        tenantId: user.id, // This is also the user ID
      };

      // Only add optional fields if they have values (not undefined)
      if (farmName) {
        processedExpenseData.farmName = farmName;
      }

      // Add animal info only for non-Labor categories and only if values exist
      if (category !== ExpenseCategory.LABOR) {
        if (animalId) {
          processedExpenseData.animalId = animalId;
        }
        if (animalName) {
          processedExpenseData.animalName = animalName;
        }
      }

      // Add staff info only for Labor category and only if values exist
      if (category === ExpenseCategory.LABOR) {
        if (staffId) {
          processedExpenseData.staffId = staffId;
        }
        if (staffName) {
          processedExpenseData.staffName = staffName;
        }
      }

      // Add receipt image only if it exists
      if (receiptImage) {
        processedExpenseData.receiptImage = receiptImage;
      }

      // Validate that farmId is a valid farm ID
      if (!farmId) {
        Alert.alert(t('common.error'), t('expenses.selectFarm'));
        return;
      }

      const farm = farms.find(f => f.id === farmId);
      if (!farm) {
        Alert.alert(t('common.error'), t('expenses.invalidFarm'));
        return;
      }

      // Upload receipt image to Firebase Storage if it exists
      if (processedExpenseData.receiptImage) {
        try {
          // Get Firebase Storage instance
          const storage = getStorage();
          const timestamp = Date.now();
          const imagePath = `expenses/${user.id}/${timestamp}.jpg`;

          const imageRef = ref(storage, imagePath);

          let blob: Blob;

          if (processedExpenseData.receiptImage.startsWith('data:image')) {
            // Handle base64 images
            const response = await fetch(processedExpenseData.receiptImage);
            blob = await response.blob();
          } else if (processedExpenseData.receiptImage.startsWith('file:') ||
            processedExpenseData.receiptImage.startsWith('content:') ||
            processedExpenseData.receiptImage.startsWith('ph:')) {
            // Handle file/content URIs
            const response = await fetch(processedExpenseData.receiptImage);
            blob = await response.blob();
          } else {
            // Use as is - might be a URL already
            throw new Error('Unsupported image URI format: ' + processedExpenseData.receiptImage.substring(0, 20));
          }

          // Upload with metadata
          const metadata = {
            contentType: 'image/jpeg',
            customMetadata: {
              'uploaded-by': user.id,
              'timestamp': timestamp.toString()
            }
          };

          await uploadBytes(imageRef, blob, metadata);

          // Get download URL
          const downloadURL = await getDownloadURL(imageRef);

          processedExpenseData.receiptImage = downloadURL;

        } catch (error) {
          console.log('Image upload failed, keeping original URI:', error);
          // Keep the original image URI if upload fails
        }
      }

      await addExpense(processedExpenseData);



      playFeedback('success');
      showToast({
        type: 'success',
        title: t('expenses.addSuccess'),
        message: t('expenses.addSuccessDetail'),
      });

      // Use setTimeout to delay navigation
      setTimeout(() => {
        try {
          // Navigate to expenses tab
          router.replace('/(tabs)/expenses');
        } catch (navError) {
          // Fallback navigation
          Alert.alert(
            t('common.success'),
            t('expenses.addSuccess'),
            [
              {
                text: 'OK',
                onPress: () => {
                  // Try again with a longer delay
                  setTimeout(() => {
                    router.replace('/(tabs)/expenses');
                  }, 800);
                }
              }
            ]
          );
        }
      }, 300);
    } catch (error) {
      console.error('Error saving expense:', error);
      playFeedback('error');
      Alert.alert(t('common.error'), t('expenses.addError'));
    }
  };

  useEffect(() => {
    if (preselectedFarmId) {
      const farm = farms.find(f => f.id === preselectedFarmId);
      if (farm) {
        setFarmId(farm.id);
        setFarmName(farm.name);
      }
    }
  }, [farms, preselectedFarmId]);

  // At the beginning of the component, add this effect to set a default farm if none is selected
  useEffect(() => {
    // If no farm is selected and farms are available, select the first farm
    if ((!farmId || farmId === '') && farms.length > 0 && !preselectedFarmId) {
      setFarmId(farms[0].id);
      setFarmName(farms[0].name);
    }
  }, [farms, farmId, preselectedFarmId]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom',]}>
      <Stack.Screen
        options={{
          title: t('expenses.addNew'),
          headerTitleStyle: { fontWeight: 'bold', color: themedColors.text },
          headerStyle: {
            backgroundColor: themedColors.background,
          },
          headerTintColor: themedColors.text,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color={themedColors.text} />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView style={styles.scrollView}>
        {/* Receipt Image - Moved to top */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('expenses.receipt')} ({t('common.optional')})</Text>

          {receiptImage ? (
            <View style={styles.imageUploadCard}>
              <View style={styles.imageContainer}>
                <Image
                  source={{ uri: receiptImage }}
                  style={styles.image}
                  resizeMode="cover"
                />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => setReceiptImage(null)}
                >
                  <X size={20} color="white" />
                </TouchableOpacity>

                <View style={styles.successIndicator}>
                  <CheckCircle2 size={20} color={themedColors.success} />
                  <Text style={styles.successText}>{t('expenses.receiptAdded')}</Text>
                </View>
              </View>

              <ImageCaptureButtons
                onTakePhoto={handleTakePhoto}
                onChooseFromLibrary={handlePickImage}
                disabled={isAnalyzingReceipt}
              />

            </View>
          ) : (
            <View style={styles.imageUploadCard}>
              <View style={styles.imagePlaceholder}>
                <Camera size={40} color={themedColors.textSecondary} />
                <Text style={styles.imagePlaceholderText}>{t('expenses.addReceipt')}*</Text>
              </View>

              <ImageCaptureButtons
                onTakePhoto={handleTakePhoto}
                onChooseFromLibrary={handlePickImage}
                disabled={isAnalyzingReceipt}
              />
            </View>
          )}

          {isAnalyzingReceipt && (
            <View style={styles.analysisStatus}>
              <ActivityIndicator size="small" color={themedColors.primary} />
              <Text style={styles.analysisStatusText}>{t('expenses.analyzing')}</Text>
            </View>
          )}

          {analysisError && (
            <View style={styles.analysisError}>
              <AlertCircle size={16} color={themedColors.error} />
              <Text style={styles.analysisErrorText}>{analysisError}</Text>
            </View>
          )}
        </View>
        {/* Amount with Currency Selector (Disabled) */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('expenses.amount')} *</Text>
          <Text style={[styles.helperText, { color: themedColors.textSecondary, marginBottom: 8 }]}>
            Currency is fixed to Pakistani Rupee (PKR) for all expenses
          </Text>
          <View style={styles.amountContainer}>
            <View
              style={[
                styles.currencySelector,
                { opacity: 0.6, backgroundColor: themedColors.border }
              ]}
            >
              <View style={styles.currencyDisplayContainer}>
                <Text style={[styles.currencyCode, { color: themedColors.textSecondary }]}>{currency}</Text>
                <Text style={[styles.currencySymbol, { color: themedColors.textSecondary }]}>
                  {currentCurrencyInfo?.symbol || CURRENCIES[currency]?.symbol || currency}
                </Text>
              </View>
              <Text style={[styles.lockedText, { color: themedColors.textSecondary }]}>🔒</Text>
            </View>
            <Input
              value={amount}
              onChangeText={setAmount}
              placeholder="0.00"
              keyboardType="numeric"
              leftIcon={null}
              error={errors.amount}
              style={styles.amountInput}
            />
          </View>
        </View>

        {/* Currency Picker Modal */}
        {showCurrencyPicker && (
          <Modal
            visible={showCurrencyPicker}
            transparent={true}
            animationType="slide"
            onRequestClose={() => {
              setShowCurrencyPicker(false);
              setCurrencySearchQuery('');
            }}
          >
            <View style={styles.modalOverlay}>
              <TouchableOpacity
                style={styles.modalBackdrop}
                activeOpacity={1}
                onPress={() => {
                  setShowCurrencyPicker(false);
                  setCurrencySearchQuery('');
                }}
              />
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>{t('expenses.selectCurrency')}</Text>

                {/* Search Input */}
                <TextInput
                  style={styles.searchInput}
                  placeholder={t('common.search') || 'Search currencies...'}
                  value={currencySearchQuery}
                  onChangeText={setCurrencySearchQuery}
                  placeholderTextColor={themedColors.textSecondary}
                />

                <FlatList
                  data={currencyOptions.filter(item => item.value !== 'SEPARATOR')}
                  keyExtractor={(item) => item.value}
                  renderItem={({ item }) => {
                    // Skip separator items
                    if (item.value === 'SEPARATOR') {
                      return (
                        <View style={styles.separatorContainer}>
                          <Text style={styles.separatorText}>Other Currencies</Text>
                        </View>
                      );
                    }

                    return (
                      <TouchableOpacity
                        style={[
                          styles.currencyOption,
                          currency === item.value && styles.selectedCurrencyOption
                        ]}
                        onPress={() => {
                          setCurrency(item.value);
                          setShowCurrencyPicker(false);
                          setCurrencySearchQuery('');
                        }}
                      >
                        <View style={styles.currencyOptionContent}>
                          <Text style={[
                            styles.currencyOptionText,
                            currency === item.value && styles.selectedCurrencyOptionText
                          ]}>
                            {item.label}
                          </Text>
                        </View>
                        {currency === item.value && (
                          <View style={styles.checkIcon}>
                            <Check size={16} color="white" />
                          </View>
                        )}
                      </TouchableOpacity>
                    );
                  }}
                />
              </View>
            </View>
          </Modal>
        )}

        {/* Date */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('expenses.date')}</Text>
          <DatePickerInput
            date={date}
            onDateChange={setDate}
            icon={<Calendar size={20} color={themedColors.text} />}
          />
        </View>

        {/* Category */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('expenses.category.label')} *</Text>
          <GenericDropdown
            items={categoryOptions}
            placeholder={t('expenses.category.select')}
            value={category || ''}
            onSelect={(value) => setCategory(value as ExpenseCategory)}
            error={errors.category}
          />
        </View>

        {/* Farm */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('farm.select')} *</Text>
          <GenericDropdown
            placeholder={t('farms.selectFarmPlaceholder')}
            items={farms.map(farm => ({
              id: farm.id,
              label: farm.name,
              description: farm.location,
              icon: <MapPin size={20} color={themedColors.primary} />
            }))}
            value={farmId || ''}
            onSelect={(id) => {
              const selectedFarm = farms.find(farm => farm.id === id);
              if (selectedFarm) {
                handleFarmSelect(selectedFarm.id, selectedFarm.name);
              }
            }}
            modalTitle={t('farms.selectFarm')}
            searchPlaceholder={t('farms.searchFarms')}
            error={errors.farmId}
          />
        </View>

        {/* Staff (shown when Labor category is selected) */}
        {farmId && category === ExpenseCategory.LABOR && (
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('expenses.selectStaff')} *</Text>
            <GenericDropdown
              placeholder={t('expenses.selectStaffPlaceholder') || "Select staff member"}
              items={staff.map((person: any) => ({
                id: person.id,
                label: person.name,
                description: person.position || person.role || "",
                imageUri: person.imageUrl,
                icon: !person.imageUrl ? <Users size={20} color={themedColors.primary} /> : undefined
              }))}
              value={staffId || ''}
              onSelect={(id) => {
                const selectedStaff = staff.find((person: any) => person.id === id);
                if (selectedStaff) {
                  handleStaffSelect(selectedStaff.id, selectedStaff.name);
                }
              }}
              modalTitle={t('expenses.selectStaff') || "Select Staff"}
              searchPlaceholder={t('expenses.searchStaff') || "Search staff"}
              error={errors.staffId}
            />
          </View>
        )}

        {/* Animal (optional) - only shown when category is NOT Labor */}
        {farmId && category !== ExpenseCategory.LABOR && (
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('animal.select')} ({t('common.optional')})</Text>
            <AnimalSearchDropdown
              placeholder={t('animal.selectPlaceholder')}
              farmId={farmId}
              value={animalId || ''}
              onSelect={(id) => setAnimalId(id)}
              onAnimalSelect={handleAnimalSelect}
            />
          </View>
        )}

        {/* Payment Method */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('expenses.paymentMethod.label')} *</Text>
          <GenericDropdown
            items={paymentMethodOptions}
            placeholder={t('expenses.paymentMethod.select')}
            value={paymentMethod || ''}
            onSelect={(value) => setPaymentMethod(value as PaymentMethod)}
            error={errors.paymentMethod}
          />
        </View>

        {/* Description */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('expenses.description')}</Text>
          <TextInput
            placeholder={t('expenses.descriptionPlaceholder')}
            value={description}
            onChangeText={(text) => {
              setDescription(text);
              calculateDescriptionHeight(text);
            }}
            multiline={true}
            style={[styles.descriptionInput, { height: descriptionHeight }]}
            textAlignVertical="top"
          />
        </View>



        {/* Submit Button */}
        <View style={styles.butonStyle}>
          <Button
            title={t('expenses.addExpense')}
            onPress={handleSubmit}
            disabled={isLoading}
            isLoading={isLoading}
            leftIcon={<Save size={20} color="white" />}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 14,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: themedColors.text,
  },
  imageButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 10,
  },

  imagePreviewContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  imagePreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    padding: 8,
  },
  colorfulIconBg: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  descriptionInput: {
    minHeight: 120,
    maxHeight: 300,
    backgroundColor: themedColors.card,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: themedColors.text,
    textAlignVertical: 'top',
    fontFamily: Platform.OS === 'ios' ? 'Courier New' : 'monospace', // Explicitly set monospaced font
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 8,
    marginRight: 2,
    borderWidth: 1,
    borderColor: themedColors.border,
    marginTop: -15,
    minWidth: 90,
    shadowColor: themedColors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.text,
    marginLeft: 6,
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: '700',
    color: themedColors.text,
    letterSpacing: 0.5,
  },
  currencyInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  currencyDisplayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'space-between',
  },
  amountInput: {
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    backgroundColor: themedColors.background,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '60%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  currencyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
    backgroundColor: themedColors.background,
  },
  selectedCurrencyOption: {
    backgroundColor: themedColors.primaryLight,
  },
  currencyOptionText: {
    fontSize: 16,
    color: themedColors.text,
    flex: 1,
    fontWeight: '500',
  },
  selectedCurrencyOptionText: {
    color: themedColors.primary,
    fontWeight: '500',
  },
  checkIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  currencyOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flagImage: {
    width: 24,
    height: 16,
    marginRight: 8,
    borderRadius: 2,
  },
  flagEmoji: {
    fontSize: 20,
    marginRight: 8,
  },
  separatorContainer: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: themedColors.card,
  },
  separatorText: {
    fontSize: 12,
    color: themedColors.textSecondary,
    fontWeight: '500',
    textAlign: 'center',
  },
  searchInput: {
    backgroundColor: themedColors.card,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: themedColors.text,
    marginBottom: 20,
    shadowColor: themedColors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  analysisOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  analysisText: {
    color: 'white',
    marginTop: 10,
    fontSize: 16,
    fontWeight: '500',
  },
  successIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  successText: {
    marginLeft: 6,
    color: themedColors.success,
    fontWeight: '500',
    fontSize: 16,
  },
  imageUploadCard: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 10,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: themedColors.border,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: themedColors.success,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  imagePlaceholder: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: themedColors.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: themedColors.card,
  },
  imagePlaceholderText: {
    marginTop: 8,
    color: themedColors.textSecondary,
    fontSize: 14,
  },
  imageButton: {
    flex: 1,
    backgroundColor: themedColors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  imageButtonText: {
    color: 'white',
    marginLeft: 8,
    fontWeight: '500',
  },
  analysisStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  analysisStatusText: {
    marginLeft: 8,
    color: themedColors.text,
    fontSize: 14,
  },
  analysisError: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.errorLight || '#FFEBEE',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  analysisErrorText: {
    marginLeft: 8,
    color: themedColors.error || '#D32F2F',
    fontSize: 14,
  },
  urduText: {
    fontFamily: 'Jameel Noori Nastaleeq',
    textAlign: 'right',
  },
  butonStyle: {
    marginBottom: 30
  },
  helperText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  lockedText: {
    fontSize: 16,
    marginLeft: 8,
  },
});



























