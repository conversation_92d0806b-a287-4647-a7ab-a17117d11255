
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Expense, ExpenseCategory } from '@/types/expense';
import { formatDate } from '@/utils/date-utils';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import { useTranslation } from '@/hooks/useTranslation';
import { getCurrencySymbol } from '@/utils/currency-utils';
import {
  Calendar,
  Cat,
  Wheat,
  Pill,
  Syringe,
  Stethoscope,
  Wrench,
  Lightbulb,
  Users,
  Hammer,
  HelpCircle,
  Banknote,
  CreditCard,
  Building,
  CircleDollarSign,
  MoreVertical
} from 'lucide-react-native';
interface ExpenseCardProps {
  expense: Expense;
  onPress: () => void;
}

const ExpenseCard: React.FC<ExpenseCardProps> = ({ expense, onPress }) => {
  const { t, language } = useTranslation();
  // Use the theme hook
  const {
    card: cardBackgroundColor,
    text: primaryTextColor,
    textSecondary: secondaryTextColor,
    // Assuming dateColor can use textSecondary or you add a specific one to your hook
    textSecondary: dateColor, 
    isDarkMode,
  } = useThemeColors();

  // Get category color and icon
  const getCategoryInfo = (category: ExpenseCategory) => {
    // For dark mode, we might want to use lighter text colors for categories
    // or ensure the provided colors have enough contrast.
    // For now, we'll adjust the text color directly in the JSX if needed.
    switch (category) {
      case ExpenseCategory.ANIMAL_PURCHASE:
        return {
          icon: <Cat size={18} color="#8E44AD" />,
          bgColor: 'rgba(142, 68, 173, 0.15)',
          textColor: '#8E44AD',
          label: 'Animal Purchase'
        };
      case ExpenseCategory.FEED:
        return {
          icon: <Wheat size={18} color="#27AE60" />,
          bgColor: 'rgba(39, 174, 96, 0.15)',
          textColor: '#27AE60',
          label: 'Feed'
        };
      case ExpenseCategory.MEDICATION:
        return {
          icon: <Pill size={18} color="#E74C3C" />,
          bgColor: 'rgba(231, 76, 60, 0.15)',
          textColor: '#E74C3C',
          label: 'Medication'
        };
      case ExpenseCategory.VACCINATION:
        return {
          icon: <Syringe size={18} color="#3498DB" />,
          bgColor: 'rgba(52, 152, 219, 0.15)',
          textColor: '#3498DB',
          label: 'Vaccination'
        };
      case ExpenseCategory.VETERINARY:
        return {
          icon: <Stethoscope size={18} color="#2ECC71" />,
          bgColor: 'rgba(46, 204, 113, 0.15)',
          textColor: '#2ECC71',
          label: 'Veterinary'
        };
      case ExpenseCategory.EQUIPMENT:
        return {
          icon: <Wrench size={18} color="#95A5A6" />,
          bgColor: 'rgba(149, 165, 166, 0.15)',
          textColor: '#95A5A6',
          label: 'Equipment'
        };
      case ExpenseCategory.UTILITIES:
        return {
          icon: <Lightbulb size={18} color="#F39C12" />,
          bgColor: 'rgba(243, 156, 18, 0.15)',
          textColor: '#F39C12',
          label: 'Utilities'
        };
      case ExpenseCategory.LABOR:
        return {
          icon: <Users size={18} color="#1ABC9C" />,
          bgColor: 'rgba(26, 188, 156, 0.15)',
          textColor: '#1ABC9C',
          label: 'Labor'
        };
      case ExpenseCategory.MAINTENANCE:
        return {
          icon: <Hammer size={18} color="#34495E" />,
          bgColor: 'rgba(52, 73, 94, 0.15)',
          textColor: '#34495E',
          label: 'Maintenance'
        };
      case ExpenseCategory.OTHER:
      default:
        return {
          icon: <HelpCircle size={18} color="#7F8C8D" />,
          bgColor: 'rgba(127, 140, 141, 0.15)',
          textColor: '#7F8C8D',
          label: 'Other'
        };
    }
  };

  // Get payment method icon
  const getPaymentMethodIcon = (method: string, iconColor: string) => {
    switch (method) {
      case 'CASH':
        return { icon: <Banknote size={14} color={iconColor} /> };
      case 'CARD':
        return { icon: <CreditCard size={14} color={iconColor} /> };
      case 'BANK_TRANSFER':
        return { icon: <Building size={14} color={iconColor} /> };
      case 'OTHER':
      default:
        return { icon: <CircleDollarSign size={14} color={iconColor} /> };
    }
  };




  // Theme-aware shadow properties
  const lightModeShadowConfig = {
    shadowColor: '#000', // Black shadow for light mode
    shadowOpacity: 0.1,  // Increased opacity further
    shadowOffset: { width: 0, height: 1 }, // Make shadow slightly lower
    shadowRadius: 3,     // Make shadow a bit sharper
    elevation: 1,        // Increased elevation for Android
  };

  const darkModeShadowConfig = {
    shadowColor: 'rgba(255,255,255,0.1)', // White shadow with 10% alpha
    shadowOpacity: 1, // Set to 1 so the color's alpha is fully effective
    shadowOffset: { width: 0, height: 2 }, // Default offset for dark
    shadowRadius: 4,     // Default radius for dark
    elevation: 2,        // Standard elevation for dark mode
  };
  const currentShadowConfig = isDarkMode ? darkModeShadowConfig : lightModeShadowConfig;

 

  const categoryInfo = getCategoryInfo(expense.category);
  const paymentInfo = getPaymentMethodIcon(expense.paymentMethod, secondaryTextColor);

  // Get locale based on language
  const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';

  return (
    <TouchableOpacity
      style={[
        styles.container, // Base styles (some will be overridden)
        {
          backgroundColor: cardBackgroundColor,
          shadowColor: currentShadowConfig.shadowColor,
          shadowOpacity: currentShadowConfig.shadowOpacity,
          shadowOffset: currentShadowConfig.shadowOffset, // Apply dynamic offset
          shadowRadius: currentShadowConfig.shadowRadius, // Apply dynamic radius
          elevation: currentShadowConfig.elevation,
        },
        isDarkMode && styles.containerDark // For any other specific dark mode overrides
       ]}
      onPress={onPress}
    >
      <View style={[styles.cardLayout, language === 'ur' && { flexDirection: 'row-reverse' }]}>
        {/* Category icon on the far left */}
        <View style={[styles.iconCircle, { backgroundColor: categoryInfo.bgColor }, language === 'ur' && { marginRight: 0, marginLeft: 12 }]}>
          {categoryInfo.icon}
        </View>

        <View style={styles.cardContent}>
          <View style={[styles.header, language === 'ur' && { flexDirection: 'row-reverse' }]}>
            <Text style={[styles.farmName, { color: primaryTextColor }]}>{expense.farmName}</Text>
            <Text style={[styles.amount, { color: primaryTextColor }]}>
              {getCurrencySymbol(expense.currency)} {expense.amount.toFixed(2)}
            </Text>
          </View>

          <View style={styles.subHeader}>
            {expense.animalName && (
              <Text style={[styles.animalName, { color: secondaryTextColor }]}>{expense.animalName}</Text>
            )}
          </View>

          <View style={styles.footer}>
            <View style={[styles.categoryBadge, { backgroundColor: categoryInfo.bgColor }]}>
              <Text style={[
                styles.categoryText,
                // In dark mode, category text might need to be lighter regardless of its original color
                { color: isDarkMode ? '#E5E7EB' : categoryInfo.textColor }
              ]}>
                {t(`expenses.category.${categoryInfo.label.toLowerCase().replace(' ', '')}`)}
              </Text>
            </View>

            <View style={[styles.paymentMethod, language === 'ur' && { flexDirection: 'row-reverse' }]}>
              {paymentInfo.icon}
              <Text style={styles.paymentText}>
                <Text style={[styles.paymentText, { color: secondaryTextColor }]}>
                  {t(
                    expense.paymentMethod?.replace('_', '').toLowerCase().trim() === 'banktransfer'
                      ? 'expenses.paymentMethod.bankTransfer'
                      : `expenses.paymentMethod.${expense.paymentMethod?.replace('_', ' ').toLowerCase()}`
                  )}
                </Text>
              </Text>
            </View>

            <Text style={[styles.date, { color: dateColor }]}>{formatDate(expense.date, locale)}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    // backgroundColor will be set dynamically
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    // Shadow properties like shadowColor, shadowOpacity, shadowOffset, 
    // shadowRadius, and elevation will be set dynamically based on the theme.
    // Default/base shadowOffset and shadowRadius can be kept here if desired,
    // but the dynamic config will take precedence.
    overflow: 'hidden',
  },
  containerDark: {
    // Add specific dark mode shadow/border if elevation/shadowOpacity isn't enough
  },
  cardLayout: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  farmName: {
    fontSize: 16,
    fontWeight: 'bold',
    // color will be set dynamically
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
    // color will be set dynamically
  },
  subHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  animalName: {
    fontSize: 14,
    // color will be set dynamically
  },
  date: {
    fontSize: 13,
    // color will be set dynamically
    marginLeft: 'auto',
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    justifyContent: 'flex-start',
    gap: 12,
  },
  leftFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 12,
  },
  categoryBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 20, // Maximum border radius for pill shape
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  paymentText: {
    fontSize: 13,
    // color will be set dynamically
  }
});

export default ExpenseCard;
