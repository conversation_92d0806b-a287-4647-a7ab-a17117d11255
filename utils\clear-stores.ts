/**
 * Utility function to clear all user-specific data from stores
 * This is used during logout to ensure no data persists between users
 */
import AsyncStorage from '@react-native-async-storage/async-storage';

export const clearAllUserStores = async () => {
  try {
    // Clear all user-specific stores
    // We use require() to avoid circular dependencies
    
    // Clear animal store
    const { useAnimalStore } = require('@/store/animal-store');
    useAnimalStore.getState().clearAnimals();
    
    // Clear farm store
    const { useFarmStore } = require('@/store/farm-store');
    useFarmStore.getState().clearFarms();
    
    // Clear record store
    const { useRecordStore } = require('@/store/record-store');
    useRecordStore.getState().clearRecords();
    
    // Clear health check store
    const { useHealthCheckStore } = require('@/store/health-check-store');
    useHealthCheckStore.getState().clearHealthChecks();
    
    // Clear milking store
    const { useMilkingStore } = require('@/store/milking-store');
    useMilkingStore.getState().clearRecords();
    
    // Clear staff store
    const { useStaffStore } = require('@/store/staff-store');
    useStaffStore.getState().clearStaff();
    
    // Clear pregnancy store
    const { usePregnancyStore } = require('@/store/pregnancy-store');
    usePregnancyStore.getState().clearPregnancies();
    
    // Clear task store
    const { useTaskStore } = require('@/store/task-store');
    useTaskStore.getState().clearTasks();
    
    // Clear expense store
    const { useExpenseStore } = require('@/store/expense-store');
    useExpenseStore.getState().clearExpenses();

    // Clear user-specific AsyncStorage items
    await AsyncStorage.multiRemove([
      'ownerEmail',
      'ownerPassword',
      'sessionRestored'
    ]);

    console.log('All user stores and AsyncStorage items cleared successfully');
  } catch (error) {
    console.error('Error clearing user stores:', error);
  }
};
